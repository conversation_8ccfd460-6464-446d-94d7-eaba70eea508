#!/usr/bin/env python3
"""
EEG源定位结果总结和分析
"""

import numpy as np
import matplotlib.pyplot as plt
import json
import os
from pathlib import Path

def load_results(results_dir):
    """加载源定位结果"""
    results = {}
    
    if not os.path.exists(results_dir):
        print(f"结果目录不存在: {results_dir}")
        return results
    
    # 查找所有方法的结果文件
    for file in os.listdir(results_dir):
        if file.endswith('_source_strength.npy'):
            method_name = file.replace('_source_strength.npy', '')
            
            # 加载数据
            strength_file = os.path.join(results_dir, file)
            position_file = os.path.join(results_dir, f'{method_name}_source_positions.npy')
            metadata_file = os.path.join(results_dir, f'{method_name}_metadata.json')
            
            if os.path.exists(strength_file) and os.path.exists(position_file):
                strength = np.load(strength_file)
                positions = np.load(position_file)
                
                metadata = {}
                if os.path.exists(metadata_file):
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                
                results[method_name] = {
                    'strength': strength,
                    'positions': positions,
                    'metadata': metadata
                }
                
                print(f"加载 {method_name}: {strength.shape}")
    
    return results

def analyze_source_localization(results):
    """分析源定位结果"""
    if not results:
        print("没有结果可分析")
        return
    
    print("\n" + "="*70)
    print("EEG源定位结果详细分析")
    print("="*70)
    
    analysis = {}
    
    for method_name, data in results.items():
        strength = data['strength']
        positions = data['positions']
        metadata = data['metadata']
        
        # 基本统计
        max_strength = np.max(strength)
        mean_strength = np.mean(strength)
        std_strength = np.std(strength)
        
        # 找到最强源
        max_idx = np.unravel_index(np.argmax(strength), strength.shape)
        max_source_idx = max_idx[0]
        max_time_idx = max_idx[1]
        max_position = positions[max_source_idx]
        
        # 时间序列分析
        time_course = strength[max_source_idx, :]
        peak_times = []
        
        # 找到峰值时间点
        from scipy.signal import find_peaks
        try:
            peaks, _ = find_peaks(time_course, height=np.max(time_course)*0.5)
            peak_times = peaks
        except:
            peak_times = [max_time_idx]
        
        # 空间分布分析
        spatial_spread = np.std(positions, axis=0)
        
        analysis[method_name] = {
            'max_strength': max_strength,
            'mean_strength': mean_strength,
            'std_strength': std_strength,
            'max_position': max_position,
            'max_time': max_time_idx,
            'peak_times': peak_times,
            'spatial_spread': spatial_spread,
            'n_sources': strength.shape[0],
            'n_timepoints': strength.shape[1]
        }
        
        print(f"\n📊 {method_name} ({metadata.get('method', 'Unknown')}):")
        print(f"   最大强度: {max_strength:.6f}")
        print(f"   平均强度: {mean_strength:.6f} ± {std_strength:.6f}")
        print(f"   最强源位置: ({max_position[0]:.3f}, {max_position[1]:.3f}, {max_position[2]:.3f}) m")
        print(f"   最强源时间: {max_time_idx} ({max_time_idx/250.0:.3f} s)")
        print(f"   峰值时间点数: {len(peak_times)}")
        print(f"   空间分布: X±{spatial_spread[0]:.3f}, Y±{spatial_spread[1]:.3f}, Z±{spatial_spread[2]:.3f} m")
        
        if 'lambda' in metadata:
            print(f"   正则化参数: {metadata['lambda']}")
        if 'n_sources_est' in metadata:
            print(f"   估计源数: {metadata['n_sources_est']}")
    
    return analysis

def compare_methods(analysis):
    """比较不同方法"""
    if not analysis:
        return
    
    print(f"\n{'='*70}")
    print("方法比较分析")
    print(f"{'='*70}")
    
    # 按最大强度排序
    sorted_by_strength = sorted(analysis.items(), 
                               key=lambda x: x[1]['max_strength'], 
                               reverse=True)
    
    print("\n🏆 按最大源强度排序:")
    for i, (method, data) in enumerate(sorted_by_strength):
        print(f"   {i+1}. {method}: {data['max_strength']:.6f}")
    
    # 空间一致性分析
    print("\n📍 最强源位置比较:")
    positions = {}
    for method, data in analysis.items():
        pos = data['max_position']
        positions[method] = pos
        print(f"   {method}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) m")
    
    # 计算位置差异
    if len(positions) > 1:
        methods = list(positions.keys())
        print(f"\n📏 方法间位置差异 (欧氏距离, mm):")
        for i, method1 in enumerate(methods):
            for j, method2 in enumerate(methods[i+1:], i+1):
                pos1 = positions[method1]
                pos2 = positions[method2]
                distance = np.linalg.norm(pos1 - pos2) * 1000  # 转换为mm
                print(f"   {method1} vs {method2}: {distance:.1f} mm")
    
    # 时间一致性分析
    print(f"\n⏰ 最强激活时间比较:")
    for method, data in analysis.items():
        time_s = data['max_time'] / 250.0
        print(f"   {method}: {data['max_time']} ({time_s:.3f} s)")

def create_comprehensive_visualization(results, save_dir='source_analysis'):
    """创建综合可视化"""
    os.makedirs(save_dir, exist_ok=True)
    
    if not results:
        print("没有结果可可视化")
        return
    
    print(f"\n📊 创建综合可视化...")
    
    # 1. 强度比较图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 最大强度比较
    methods = list(results.keys())
    max_strengths = [np.max(results[m]['strength']) for m in methods]
    
    ax = axes[0, 0]
    bars = ax.bar(methods, max_strengths, color=['skyblue', 'lightgreen', 'orange', 'lightcoral', 'plum'][:len(methods)])
    ax.set_title('最大源强度比较', fontweight='bold')
    ax.set_ylabel('源强度')
    ax.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, strength in zip(bars, max_strengths):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(max_strengths)*0.01,
                f'{strength:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 时间序列比较
    ax = axes[0, 1]
    for i, (method, data) in enumerate(results.items()):
        strength = data['strength']
        # 选择最强源的时间序列
        max_source_idx = np.argmax(np.max(strength, axis=1))
        time_series = strength[max_source_idx, :]
        
        # 采样显示（避免数据点过多）
        step = max(1, len(time_series) // 1000)
        time_points = np.arange(0, len(time_series), step) / 250.0  # 转换为秒
        sampled_series = time_series[::step]
        
        ax.plot(time_points, sampled_series, label=method, alpha=0.8)
    
    ax.set_title('最强源时间序列', fontweight='bold')
    ax.set_xlabel('时间 (s)')
    ax.set_ylabel('源强度')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 空间分布比较 (XY平面)
    ax = axes[1, 0]
    for i, (method, data) in enumerate(results.items()):
        positions = data['positions']
        strength = data['strength']
        
        # 选择强度最大的时间点
        max_time_idx = np.argmax(np.sum(strength, axis=0))
        current_strength = strength[:, max_time_idx]
        
        # 只显示强度较大的源点
        threshold = np.max(current_strength) * 0.1
        strong_indices = current_strength > threshold
        
        if np.any(strong_indices):
            scatter = ax.scatter(positions[strong_indices, 0], positions[strong_indices, 1], 
                               c=current_strength[strong_indices], 
                               s=20, alpha=0.6, label=method, cmap='hot')
    
    ax.set_title('源分布 (XY平面)', fontweight='bold')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.legend()
    
    # 最强源位置比较
    ax = axes[1, 1]
    positions_3d = []
    method_names = []
    
    for method, data in results.items():
        strength = data['strength']
        positions = data['positions']
        max_source_idx = np.argmax(np.max(strength, axis=1))
        max_pos = positions[max_source_idx]
        positions_3d.append(max_pos)
        method_names.append(method)
    
    positions_3d = np.array(positions_3d)
    
    scatter = ax.scatter(positions_3d[:, 0], positions_3d[:, 1], 
                        c=range(len(method_names)), s=100, cmap='tab10')
    
    for i, (x, y, method) in enumerate(zip(positions_3d[:, 0], positions_3d[:, 1], method_names)):
        ax.annotate(method, (x, y), xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    ax.set_title('最强源位置比较', fontweight='bold')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"   综合分析图已保存: {save_dir}/comprehensive_analysis.png")

def main():
    """主函数"""
    print("EEG源定位结果分析")
    print("="*50)
    
    # 检查可用的结果目录
    result_dirs = ['source_localization_results', 'demo_results']
    
    for results_dir in result_dirs:
        if os.path.exists(results_dir):
            print(f"\n分析目录: {results_dir}")
            print("-" * 30)
            
            # 加载结果
            results = load_results(results_dir)
            
            if results:
                # 分析结果
                analysis = analyze_source_localization(results)
                
                # 比较方法
                compare_methods(analysis)
                
                # 创建可视化
                create_comprehensive_visualization(results, f'{results_dir}_analysis')
            else:
                print("没有找到有效的源定位结果")

if __name__ == "__main__":
    main()
