#!/usr/bin/env python3
"""
修正的MUSIC算法EEG源定位分析器
基于正确的MUSIC算法实现原理
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import linalg
from scipy.signal import find_peaks
import nibabel as nib
from eeg_source_localization import EEGSourceLocalizer
import warnings
warnings.filterwarnings('ignore')

class CorrectedMUSICAnalyzer:
    """修正的MUSIC算法分析器"""
    
    def __init__(self, bem_output_dir='hdbet_bem_output', eeg_file='signal-1.csv.gz'):
        """初始化分析器"""
        self.bem_output_dir = bem_output_dir
        self.eeg_file = eeg_file
        
        # 基础数据
        self.eeg_data = None
        self.channel_names = None
        self.sampling_rate = 250.0
        self.leadfield = None
        self.source_positions = None
        
        # 分析结果
        self.music_results = {}
        
        print("修正的MUSIC分析器初始化完成")
    
    def load_data(self):
        """加载EEG数据和正向模型"""
        print("正在加载数据...")
        
        # 使用现有的源定位器加载数据
        localizer = EEGSourceLocalizer(self.bem_output_dir, self.eeg_file)
        
        # 加载所有必要数据
        if not localizer.load_eeg_data():
            return False
        if not localizer.load_bem_model():
            return False
        if not localizer.create_source_space():
            return False
        if not localizer.compute_forward_model():
            return False
        
        # 提取数据
        self.eeg_data = localizer.eeg_data.get_data()
        self.channel_names = localizer.eeg_info['ch_names']
        self.leadfield = localizer.forward_model['sol']['data']
        self.source_positions = localizer.source_space['rr']
        
        print(f"数据加载完成:")
        print(f"  通道数: {len(self.channel_names)}")
        print(f"  时间点: {self.eeg_data.shape[1]}")
        print(f"  源点数: {len(self.source_positions)}")
        
        return True
    
    def correct_music_analysis(self, n_sources=3, time_window=2.0):
        """
        修正的MUSIC算法分析
        
        Parameters:
        -----------
        n_sources : int
            估计的源数量
        time_window : float
            时间窗口长度(秒)
        """
        print(f"\n🎼 修正的MUSIC算法分析")
        print(f"估计源数: {n_sources}")
        print(f"时间窗口: {time_window}秒")
        
        # 选择时间窗口
        window_samples = int(time_window * self.sampling_rate)
        
        # 找到信号能量最大的时间段
        total_power = np.sum(self.eeg_data**2, axis=0)
        max_power_idx = np.argmax(total_power)
        
        # 确保窗口在数据范围内
        start_idx = max(0, max_power_idx - window_samples//2)
        end_idx = min(self.eeg_data.shape[1], start_idx + window_samples)
        start_idx = max(0, end_idx - window_samples)
        
        # 提取时间窗口数据
        window_data = self.eeg_data[:, start_idx:end_idx]
        
        print(f"分析时间窗口: {start_idx/self.sampling_rate:.2f}s - {end_idx/self.sampling_rate:.2f}s")
        
        # 1. 计算数据协方差矩阵
        print("计算数据协方差矩阵...")
        data_cov = np.cov(window_data)
        
        # 添加正则化避免奇异矩阵
        reg_param = 1e-6 * np.trace(data_cov) / data_cov.shape[0]
        data_cov_reg = data_cov + reg_param * np.eye(data_cov.shape[0])
        
        # 2. 特征值分解
        print("进行特征值分解...")
        eigenvals, eigenvecs = linalg.eigh(data_cov_reg)
        
        # 按特征值降序排列
        idx = np.argsort(eigenvals)[::-1]
        eigenvals = eigenvals[idx]
        eigenvecs = eigenvecs[:, idx]
        
        print(f"特征值范围: {eigenvals[-1]:.2e} - {eigenvals[0]:.2e}")
        
        # 3. 确定信号子空间和噪声子空间
        # 使用特征值的显著下降来自动确定源数
        eigenval_ratios = eigenvals[:-1] / eigenvals[1:]
        auto_n_sources = np.argmax(eigenval_ratios) + 1
        
        # 使用用户指定的源数或自动检测的源数
        final_n_sources = min(n_sources, auto_n_sources, len(eigenvals)-1)
        
        print(f"自动检测源数: {auto_n_sources}")
        print(f"最终使用源数: {final_n_sources}")
        
        # 信号子空间和噪声子空间
        signal_subspace = eigenvecs[:, :final_n_sources]
        noise_subspace = eigenvecs[:, final_n_sources:]
        
        # 4. 计算MUSIC伪谱
        print("计算MUSIC伪谱...")
        n_sources_spatial = len(self.source_positions)
        music_spectrum = np.zeros(n_sources_spatial)
        
        # 对每个可能的源位置计算MUSIC伪谱
        for i in range(n_sources_spatial):
            if i % 1000 == 0:
                print(f"  进度: {i}/{n_sources_spatial}")
            
            # 获取该源点的导联场向量 (steering vector)
            # 取3个方向的平均或最强方向
            L_i = self.leadfield[:, i*3:(i+1)*3]  # 3个方向
            
            # 选择最强的方向作为steering vector
            max_norm_idx = np.argmax([np.linalg.norm(L_i[:, j]) for j in range(3)])
            steering_vector = L_i[:, max_norm_idx]
            
            # 标准化steering vector
            if np.linalg.norm(steering_vector) > 0:
                steering_vector = steering_vector / np.linalg.norm(steering_vector)
                
                # MUSIC伪谱公式: P_MUSIC = 1 / (a^H * En * En^H * a)
                # 其中 a 是steering vector, En 是噪声子空间
                if noise_subspace.shape[1] > 0:
                    projection = noise_subspace @ noise_subspace.T @ steering_vector
                    denominator = np.real(steering_vector.T @ projection)
                    
                    # 避免除零
                    if abs(denominator) > 1e-12:
                        music_spectrum[i] = 1.0 / abs(denominator)
                    else:
                        music_spectrum[i] = 1e12  # 很大的值表示可能的源位置
                else:
                    music_spectrum[i] = 0
            else:
                music_spectrum[i] = 0
        
        # 5. 后处理和标准化
        # 去除异常值
        music_spectrum = np.clip(music_spectrum, 0, np.percentile(music_spectrum, 99.9))
        
        # 对数变换以压缩动态范围
        music_spectrum_log = np.log10(music_spectrum + 1e-12)
        
        # 标准化到[0,1]
        if np.max(music_spectrum_log) > np.min(music_spectrum_log):
            music_spectrum_norm = (music_spectrum_log - np.min(music_spectrum_log)) / \
                                (np.max(music_spectrum_log) - np.min(music_spectrum_log))
        else:
            music_spectrum_norm = np.zeros_like(music_spectrum_log)
        
        # 6. 找到峰值源
        peaks, properties = find_peaks(music_spectrum_norm, 
                                     height=0.7,  # 只保留高峰值
                                     distance=100)  # 最小距离
        
        # 按强度排序峰值
        if len(peaks) > 0:
            peak_heights = music_spectrum_norm[peaks]
            sorted_indices = np.argsort(peak_heights)[::-1]
            top_peaks = peaks[sorted_indices[:10]]  # 取前10个峰值
        else:
            top_peaks = []
        
        # 保存结果
        self.music_results = {
            'spectrum_raw': music_spectrum,
            'spectrum_log': music_spectrum_log,
            'spectrum_norm': music_spectrum_norm,
            'eigenvals': eigenvals,
            'n_sources': final_n_sources,
            'auto_n_sources': auto_n_sources,
            'peaks': top_peaks,
            'peak_positions': self.source_positions[top_peaks] if len(top_peaks) > 0 else np.array([]),
            'peak_strengths': music_spectrum_norm[top_peaks] if len(top_peaks) > 0 else np.array([]),
            'time_window': (start_idx/self.sampling_rate, end_idx/self.sampling_rate),
            'snr_estimate': eigenvals[0] / np.mean(eigenvals[final_n_sources:]) if final_n_sources < len(eigenvals) else 1.0
        }
        
        print(f"✅ MUSIC分析完成!")
        print(f"  检测到 {len(top_peaks)} 个显著源")
        print(f"  信噪比估计: {self.music_results['snr_estimate']:.2f}")
        print(f"  谱值范围: {np.min(music_spectrum_norm):.3f} - {np.max(music_spectrum_norm):.3f}")
        
        return True
    
    def analyze_source_characteristics(self):
        """分析检测到的源特性"""
        if not self.music_results or len(self.music_results['peaks']) == 0:
            print("没有检测到显著源")
            return
        
        print(f"\n🎯 检测到的源特性分析")
        print("="*50)
        
        peaks = self.music_results['peaks']
        positions = self.music_results['peak_positions']
        strengths = self.music_results['peak_strengths']
        
        for i, (peak_idx, pos, strength) in enumerate(zip(peaks, positions, strengths)):
            print(f"\n源 #{i+1}:")
            print(f"  位置: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}) m")
            print(f"  MUSIC强度: {strength:.4f}")
            print(f"  解剖区域: {self._get_anatomical_region(pos)}")
            
            # 分析该源的频域特性
            source_leadfield = self.leadfield[:, peak_idx*3:(peak_idx+1)*3]
            max_direction = np.argmax([np.linalg.norm(source_leadfield[:, j]) for j in range(3)])
            steering_vector = source_leadfield[:, max_direction]
            
            # 计算该源对各通道的贡献
            contributions = np.abs(steering_vector)
            max_contrib_ch = np.argmax(contributions)
            
            print(f"  主要贡献通道: {self.channel_names[max_contrib_ch]} ({contributions[max_contrib_ch]:.3f})")
            print(f"  通道贡献分布: 前3名 - ", end="")
            
            top3_channels = np.argsort(contributions)[::-1][:3]
            for j, ch_idx in enumerate(top3_channels):
                print(f"{self.channel_names[ch_idx]}({contributions[ch_idx]:.2f})", end="")
                if j < 2:
                    print(", ", end="")
            print()
    
    def _get_anatomical_region(self, position):
        """根据位置推断解剖区域"""
        x, y, z = position
        
        # 更精确的解剖区域判断
        if y > 0.06:  # 前部
            if abs(x) < 0.015:
                return "正中前额叶"
            elif x < -0.015:
                return "左前额叶"
            else:
                return "右前额叶"
        elif y < -0.06:  # 后部
            if z > 0.05:  # 高位
                if abs(x) < 0.015:
                    return "正中顶叶"
                elif x < -0.015:
                    return "左顶叶"
                else:
                    return "右顶叶"
            else:  # 低位
                if abs(x) < 0.015:
                    return "正中枕叶"
                elif x < -0.015:
                    return "左枕叶"
                else:
                    return "右枕叶"
        else:  # 中部
            if abs(x) < 0.015:
                return "正中中央区"
            elif x < -0.015:
                return "左中央区"
            else:
                return "右中央区"
    
    def visualize_results(self, save_dir='corrected_music_results'):
        """可视化修正的MUSIC结果"""
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        if not self.music_results:
            print("没有MUSIC结果可显示")
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 特征值分布
        ax = axes[0, 0]
        eigenvals = self.music_results['eigenvals']
        ax.semilogy(eigenvals, 'bo-')
        ax.axvline(self.music_results['n_sources'], color='red', linestyle='--', 
                  label=f'信号子空间维度: {self.music_results["n_sources"]}')
        ax.set_title('特征值分布', fontweight='bold')
        ax.set_xlabel('特征值索引')
        ax.set_ylabel('特征值 (对数尺度)')
        ax.legend()
        ax.grid(True)
        
        # 2. MUSIC伪谱分布
        ax = axes[0, 1]
        spectrum = self.music_results['spectrum_norm']
        positions = self.source_positions
        
        scatter = ax.scatter(positions[:, 0], positions[:, 1], 
                           c=spectrum, cmap='hot', s=2, alpha=0.6)
        
        # 标记峰值源
        if len(self.music_results['peaks']) > 0:
            peak_pos = self.music_results['peak_positions']
            ax.scatter(peak_pos[:, 0], peak_pos[:, 1], 
                      c='blue', s=100, marker='*', edgecolor='white', linewidth=1)
            
            # 添加标签
            for i, pos in enumerate(peak_pos[:5]):  # 只标记前5个
                ax.annotate(f'S{i+1}', (pos[0], pos[1]), 
                          xytext=(5, 5), textcoords='offset points',
                          fontsize=10, color='blue', fontweight='bold')
        
        ax.set_title('MUSIC伪谱 (XY平面)', fontweight='bold')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        plt.colorbar(scatter, ax=ax, label='MUSIC强度')
        
        # 3. 源强度直方图
        ax = axes[0, 2]
        ax.hist(spectrum, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax.axvline(np.mean(spectrum), color='red', linestyle='--', label=f'均值: {np.mean(spectrum):.3f}')
        ax.axvline(np.percentile(spectrum, 95), color='orange', linestyle='--', 
                  label=f'95%分位: {np.percentile(spectrum, 95):.3f}')
        ax.set_title('MUSIC强度分布', fontweight='bold')
        ax.set_xlabel('MUSIC强度')
        ax.set_ylabel('频次')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 4. XZ平面投影
        ax = axes[1, 0]
        scatter = ax.scatter(positions[:, 0], positions[:, 2], 
                           c=spectrum, cmap='hot', s=2, alpha=0.6)
        
        if len(self.music_results['peaks']) > 0:
            peak_pos = self.music_results['peak_positions']
            ax.scatter(peak_pos[:, 0], peak_pos[:, 2], 
                      c='blue', s=100, marker='*', edgecolor='white', linewidth=1)
        
        ax.set_title('MUSIC伪谱 (XZ平面)', fontweight='bold')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Z (m)')
        plt.colorbar(scatter, ax=ax, label='MUSIC强度')
        
        # 5. YZ平面投影
        ax = axes[1, 1]
        scatter = ax.scatter(positions[:, 1], positions[:, 2], 
                           c=spectrum, cmap='hot', s=2, alpha=0.6)
        
        if len(self.music_results['peaks']) > 0:
            peak_pos = self.music_results['peak_positions']
            ax.scatter(peak_pos[:, 1], peak_pos[:, 2], 
                      c='blue', s=100, marker='*', edgecolor='white', linewidth=1)
        
        ax.set_title('MUSIC伪谱 (YZ平面)', fontweight='bold')
        ax.set_xlabel('Y (m)')
        ax.set_ylabel('Z (m)')
        plt.colorbar(scatter, ax=ax, label='MUSIC强度')
        
        # 6. 信噪比和质量指标
        ax = axes[1, 2]
        
        # 显示关键指标
        metrics = [
            f"检测源数: {len(self.music_results['peaks'])}",
            f"信噪比: {self.music_results['snr_estimate']:.2f}",
            f"信号子空间维度: {self.music_results['n_sources']}",
            f"最大MUSIC强度: {np.max(spectrum):.4f}",
            f"平均MUSIC强度: {np.mean(spectrum):.4f}",
            f"时间窗口: {self.music_results['time_window'][0]:.1f}-{self.music_results['time_window'][1]:.1f}s"
        ]
        
        ax.text(0.1, 0.9, '\n'.join(metrics), transform=ax.transAxes, 
               fontsize=12, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax.set_title('分析指标', fontweight='bold')
        ax.axis('off')
        
        plt.tight_layout()
        plt.savefig(f'{save_dir}/corrected_music_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 可视化结果已保存: {save_dir}/corrected_music_analysis.png")


def main():
    """主函数"""
    import os
    
    print("🎼 修正的MUSIC算法EEG源定位分析")
    print("="*60)
    
    # 检查必要文件
    required_files = ['signal-1.csv.gz', 'hdbet_bem_output']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return
    
    try:
        # 创建分析器
        analyzer = CorrectedMUSICAnalyzer()
        
        # 加载数据
        if not analyzer.load_data():
            print("❌ 数据加载失败")
            return
        
        # 运行修正的MUSIC分析
        if analyzer.correct_music_analysis(n_sources=5, time_window=2.0):
            # 分析源特性
            analyzer.analyze_source_characteristics()
            
            # 可视化结果
            analyzer.visualize_results()
            
            print(f"\n🎉 修正的MUSIC分析完成!")
        else:
            print("❌ MUSIC分析失败")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
