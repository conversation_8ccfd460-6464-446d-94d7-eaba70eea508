#!/usr/bin/env python3
"""
MUSIC算法逐通道详细分析器
对每个EEG通道进行独立的MUSIC源定位分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import linalg
from scipy.signal import find_peaks, welch
import nibabel as nib
from eeg_source_localization import EEGSourceLocalizer
import warnings
warnings.filterwarnings('ignore')

class MUSICChannelAnalyzer:
    """MUSIC算法逐通道分析器"""
    
    def __init__(self, bem_output_dir='hdbet_bem_output', eeg_file='signal-1.csv.gz'):
        """初始化分析器"""
        self.bem_output_dir = bem_output_dir
        self.eeg_file = eeg_file
        
        # 基础数据
        self.eeg_data = None
        self.channel_names = None
        self.sampling_rate = 250.0
        self.leadfield = None
        self.source_positions = None
        
        # 分析结果
        self.channel_results = {}
        self.channel_statistics = {}
        
        # 标准10-20电极位置和解剖对应
        self.electrode_anatomy = {
            'Fp1': '左前额极',
            'Fp2': '右前额极', 
            'F7': '左前颞',
            'F3': '左前额',
            'Fz': '正中前额',
            'F4': '右前额',
            'F8': '右前颞',
            'T7': '左颞',
            'C3': '左中央',
            'Cz': '正中中央',
            'C4': '右中央',
            'T8': '右颞',
            'P7': '左后颞',
            'P3': '左顶',
            'Pz': '正中顶',
            'P4': '右顶',
            'P8': '右后颞',
            'O1': '左枢',
            'O2': '右枕'
        }
        
        print("MUSIC逐通道分析器初始化完成")
    
    def load_data(self):
        """加载EEG数据和正向模型"""
        print("正在加载数据...")
        
        # 使用现有的源定位器加载数据
        localizer = EEGSourceLocalizer(self.bem_output_dir, self.eeg_file)
        
        # 加载所有必要数据
        if not localizer.load_eeg_data():
            return False
        if not localizer.load_bem_model():
            return False
        if not localizer.create_source_space():
            return False
        if not localizer.compute_forward_model():
            return False
        
        # 提取数据
        self.eeg_data = localizer.eeg_data.get_data()
        self.channel_names = localizer.eeg_info['ch_names']
        self.leadfield = localizer.forward_model['sol']['data']
        self.source_positions = localizer.source_space['rr']
        
        print(f"数据加载完成:")
        print(f"  通道数: {len(self.channel_names)}")
        print(f"  时间点: {self.eeg_data.shape[1]}")
        print(f"  源点数: {len(self.source_positions)}")
        
        return True
    
    def analyze_single_channel_music(self, channel_idx, n_sources_range=[1, 2, 3, 4, 5]):
        """对单个通道进行MUSIC分析"""
        channel_name = self.channel_names[channel_idx]
        channel_data = self.eeg_data[channel_idx:channel_idx+1, :]  # 保持2D形状
        
        print(f"\n🎵 分析通道 {channel_name} ({self.electrode_anatomy.get(channel_name, '未知区域')})")
        
        results = {
            'channel_name': channel_name,
            'anatomy': self.electrode_anatomy.get(channel_name, '未知区域'),
            'signal_stats': {},
            'music_results': {},
            'optimal_sources': None,
            'peak_sources': [],
            'time_analysis': {}
        }
        
        # 1. 信号统计分析
        results['signal_stats'] = self._analyze_signal_statistics(channel_data[0])
        
        # 2. 对不同源数进行MUSIC分析
        best_score = -np.inf
        best_n_sources = 1
        
        for n_sources in n_sources_range:
            print(f"  测试 {n_sources} 个源...")
            
            music_result = self._single_channel_music(channel_data, n_sources)
            results['music_results'][n_sources] = music_result
            
            # 评估结果质量
            if music_result['success']:
                score = self._evaluate_music_quality(music_result)
                if score > best_score:
                    best_score = score
                    best_n_sources = n_sources
        
        results['optimal_sources'] = best_n_sources
        
        # 3. 使用最优源数进行详细分析
        if best_n_sources in results['music_results']:
            best_result = results['music_results'][best_n_sources]
            if best_result['success']:
                results['peak_sources'] = self._find_peak_sources(best_result)
                results['time_analysis'] = self._analyze_temporal_dynamics(
                    channel_data[0], best_result)
        
        print(f"  ✅ 最优源数: {best_n_sources}, 质量评分: {best_score:.3f}")
        
        return results
    
    def _analyze_signal_statistics(self, signal):
        """分析信号统计特性"""
        stats = {
            'mean': np.mean(signal),
            'std': np.std(signal),
            'min': np.min(signal),
            'max': np.max(signal),
            'range': np.max(signal) - np.min(signal),
            'rms': np.sqrt(np.mean(signal**2)),
            'skewness': self._calculate_skewness(signal),
            'kurtosis': self._calculate_kurtosis(signal)
        }
        
        # 频域分析
        freqs, psd = welch(signal, fs=self.sampling_rate, nperseg=1024)
        
        # 频带功率
        delta_power = np.mean(psd[(freqs >= 1) & (freqs <= 4)])
        theta_power = np.mean(psd[(freqs >= 4) & (freqs <= 8)])
        alpha_power = np.mean(psd[(freqs >= 8) & (freqs <= 13)])
        beta_power = np.mean(psd[(freqs >= 13) & (freqs <= 30)])
        gamma_power = np.mean(psd[(freqs >= 30) & (freqs <= 100)])
        
        stats.update({
            'delta_power': delta_power,
            'theta_power': theta_power,
            'alpha_power': alpha_power,
            'beta_power': beta_power,
            'gamma_power': gamma_power,
            'dominant_freq': freqs[np.argmax(psd)],
            'total_power': np.sum(psd)
        })
        
        return stats
    
    def _calculate_skewness(self, data):
        """计算偏度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 3)
    
    def _calculate_kurtosis(self, data):
        """计算峰度"""
        mean = np.mean(data)
        std = np.std(data)
        return np.mean(((data - mean) / std) ** 4) - 3
    
    def _single_channel_music(self, channel_data, n_sources):
        """对单通道执行MUSIC算法"""
        try:
            # 计算数据协方差矩阵
            data_cov = np.cov(channel_data)
            
            # 对于单通道，我们需要构造一个时间延迟嵌入矩阵
            # 或者使用空间平滑技术
            embedded_data = self._create_embedded_matrix(channel_data[0])
            data_cov = np.cov(embedded_data)
            
            # 特征值分解
            eigenvals, eigenvecs = linalg.eigh(data_cov)
            
            # 按特征值降序排列
            idx = np.argsort(eigenvals)[::-1]
            eigenvals = eigenvals[idx]
            eigenvecs = eigenvecs[:, idx]
            
            # 信号子空间和噪声子空间
            signal_subspace = eigenvecs[:, :n_sources]
            noise_subspace = eigenvecs[:, n_sources:]
            
            # 计算MUSIC伪谱
            n_sources_spatial = len(self.source_positions)
            music_spectrum = np.zeros(n_sources_spatial)
            
            # 为单通道调整导联场矩阵
            channel_leadfield = self.leadfield[0:1, :]  # 只取第一个通道
            
            for i in range(n_sources_spatial):
                # 获取该源点的导联场向量
                L_i = channel_leadfield[:, i*3:(i+1)*3]  # 3个方向
                
                # 对每个方向计算MUSIC伪谱
                max_music_val = 0
                for j in range(3):
                    if L_i.shape[1] > j:
                        l = L_i[:, j]
                        if np.linalg.norm(l) > 0:
                            l = l / np.linalg.norm(l)
                            
                            # 扩展到嵌入维度
                            l_embedded = np.tile(l, embedded_data.shape[0] // len(l))
                            if len(l_embedded) < embedded_data.shape[0]:
                                l_embedded = np.pad(l_embedded, (0, embedded_data.shape[0] - len(l_embedded)))
                            elif len(l_embedded) > embedded_data.shape[0]:
                                l_embedded = l_embedded[:embedded_data.shape[0]]
                            
                            # MUSIC伪谱计算
                            if noise_subspace.shape[1] > 0:
                                proj = noise_subspace @ noise_subspace.T @ l_embedded
                                denominator = l_embedded.T @ proj
                                if abs(denominator) > 1e-10:
                                    music_val = 1.0 / abs(denominator)
                                    max_music_val = max(max_music_val, music_val)
                
                music_spectrum[i] = max_music_val
            
            # 标准化
            if np.max(music_spectrum) > 0:
                music_spectrum = music_spectrum / np.max(music_spectrum)
            
            return {
                'success': True,
                'spectrum': music_spectrum,
                'eigenvals': eigenvals,
                'n_sources': n_sources,
                'signal_subspace_dim': n_sources,
                'noise_subspace_dim': len(eigenvals) - n_sources
            }
            
        except Exception as e:
            print(f"    MUSIC计算失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'spectrum': np.zeros(len(self.source_positions)),
                'n_sources': n_sources
            }
    
    def _create_embedded_matrix(self, signal, embedding_dim=10):
        """创建时间延迟嵌入矩阵"""
        n_samples = len(signal) - embedding_dim + 1
        embedded = np.zeros((embedding_dim, n_samples))
        
        for i in range(embedding_dim):
            embedded[i, :] = signal[i:i+n_samples]
        
        return embedded
    
    def _evaluate_music_quality(self, music_result):
        """评估MUSIC结果质量"""
        if not music_result['success']:
            return -np.inf
        
        spectrum = music_result['spectrum']
        eigenvals = music_result['eigenvals']
        
        # 评估指标
        # 1. 峰值清晰度
        peak_clarity = np.max(spectrum) - np.mean(spectrum)
        
        # 2. 特征值比例
        if len(eigenvals) > music_result['n_sources']:
            signal_power = np.sum(eigenvals[:music_result['n_sources']])
            noise_power = np.sum(eigenvals[music_result['n_sources']:])
            snr_ratio = signal_power / (noise_power + 1e-10)
        else:
            snr_ratio = 1.0
        
        # 3. 谱的稀疏性
        sparsity = len(spectrum) / (np.sum(spectrum > 0.1 * np.max(spectrum)) + 1)
        
        # 综合评分
        score = peak_clarity * np.log(snr_ratio + 1) * np.log(sparsity + 1)
        
        return score
    
    def _find_peak_sources(self, music_result):
        """找到MUSIC谱的峰值源"""
        spectrum = music_result['spectrum']
        
        # 找到峰值
        peaks, properties = find_peaks(spectrum, 
                                     height=0.3 * np.max(spectrum),
                                     distance=50)  # 最小距离50个源点
        
        peak_sources = []
        for peak_idx in peaks:
            peak_info = {
                'source_idx': peak_idx,
                'position': self.source_positions[peak_idx],
                'strength': spectrum[peak_idx],
                'anatomical_region': self._get_anatomical_region(self.source_positions[peak_idx])
            }
            peak_sources.append(peak_info)
        
        # 按强度排序
        peak_sources.sort(key=lambda x: x['strength'], reverse=True)
        
        return peak_sources
    
    def _get_anatomical_region(self, position):
        """根据位置推断解剖区域"""
        x, y, z = position
        
        # 简化的解剖区域判断
        if y > 0.05:  # 前部
            if abs(x) < 0.02:
                return "正中前额叶"
            elif x < 0:
                return "左前额叶"
            else:
                return "右前额叶"
        elif y < -0.05:  # 后部
            if abs(x) < 0.02:
                return "正中枕叶"
            elif x < 0:
                return "左枕叶"
            else:
                return "右枕叶"
        else:  # 中部
            if abs(x) < 0.02:
                return "正中中央区"
            elif x < 0:
                return "左中央区"
            else:
                return "右中央区"
    
    def _analyze_temporal_dynamics(self, signal, music_result):
        """分析时间动态特性"""
        # 滑动窗口MUSIC分析
        window_size = int(2 * self.sampling_rate)  # 2秒窗口
        step_size = int(0.5 * self.sampling_rate)   # 0.5秒步长
        
        n_windows = (len(signal) - window_size) // step_size + 1
        temporal_spectrum = np.zeros((n_windows, len(self.source_positions)))
        
        for i in range(n_windows):
            start_idx = i * step_size
            end_idx = start_idx + window_size
            window_data = signal[start_idx:end_idx]
            
            # 对窗口数据进行MUSIC分析
            window_result = self._single_channel_music(
                window_data.reshape(1, -1), 
                music_result['n_sources']
            )
            
            if window_result['success']:
                temporal_spectrum[i, :] = window_result['spectrum']
        
        return {
            'temporal_spectrum': temporal_spectrum,
            'time_points': np.arange(n_windows) * step_size / self.sampling_rate,
            'window_size_s': window_size / self.sampling_rate,
            'step_size_s': step_size / self.sampling_rate
        }

    def analyze_all_channels(self):
        """分析所有通道"""
        print("\n🎼 开始MUSIC逐通道完整分析")
        print("="*60)

        if not self.load_data():
            print("数据加载失败")
            return False

        # 分析每个通道
        for i, channel_name in enumerate(self.channel_names):
            print(f"\n进度: {i+1}/{len(self.channel_names)}")

            result = self.analyze_single_channel_music(i)
            self.channel_results[channel_name] = result

            # 计算通道统计
            self.channel_statistics[channel_name] = self._compute_channel_statistics(result)

        print(f"\n✅ 所有{len(self.channel_names)}个通道分析完成!")
        return True

    def _compute_channel_statistics(self, channel_result):
        """计算通道统计信息"""
        stats = {
            'channel_name': channel_result['channel_name'],
            'anatomy': channel_result['anatomy'],
            'optimal_n_sources': channel_result['optimal_sources'],
            'n_peak_sources': len(channel_result['peak_sources']),
            'max_music_strength': 0,
            'dominant_source_region': 'None',
            'signal_quality': 'Unknown'
        }

        # 从最优MUSIC结果提取信息
        if channel_result['optimal_sources'] in channel_result['music_results']:
            optimal_result = channel_result['music_results'][channel_result['optimal_sources']]
            if optimal_result['success']:
                stats['max_music_strength'] = np.max(optimal_result['spectrum'])

        # 主导源区域
        if channel_result['peak_sources']:
            stats['dominant_source_region'] = channel_result['peak_sources'][0]['anatomical_region']

        # 信号质量评估
        signal_stats = channel_result['signal_stats']
        snr_estimate = signal_stats['rms'] / (signal_stats['std'] + 1e-10)

        if snr_estimate > 2.0:
            stats['signal_quality'] = 'Good'
        elif snr_estimate > 1.0:
            stats['signal_quality'] = 'Fair'
        else:
            stats['signal_quality'] = 'Poor'

        return stats

    def create_comprehensive_report(self, save_dir='music_analysis_results'):
        """创建综合分析报告"""
        import os
        os.makedirs(save_dir, exist_ok=True)

        print(f"\n📊 生成MUSIC分析综合报告...")

        # 1. 通道概览表
        self._create_channel_overview_table(save_dir)

        # 2. 信号质量分析
        self._create_signal_quality_analysis(save_dir)

        # 3. 源定位结果可视化
        self._create_source_localization_visualization(save_dir)

        print(f"📋 综合报告已保存到: {save_dir}/")

    def _create_channel_overview_table(self, save_dir):
        """创建通道概览表"""
        # 准备数据
        overview_data = []
        for channel_name, stats in self.channel_statistics.items():
            row = {
                '通道': channel_name,
                '解剖位置': stats['anatomy'],
                '最优源数': stats['optimal_n_sources'],
                '峰值源数': stats['n_peak_sources'],
                'MUSIC强度': f"{stats['max_music_strength']:.3f}",
                '主导源区域': stats['dominant_source_region'],
                '信号质量': stats['signal_quality']
            }
            overview_data.append(row)

        # 创建DataFrame并保存
        df = pd.DataFrame(overview_data)
        df.to_csv(f"{save_dir}/channel_overview.csv", index=False, encoding='utf-8-sig')

        # 创建可视化表格
        fig, ax = plt.subplots(figsize=(16, 10))
        ax.axis('tight')
        ax.axis('off')

        table = ax.table(cellText=df.values, colLabels=df.columns,
                        cellLoc='center', loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.2, 1.5)

        # 根据信号质量着色
        for i, row in df.iterrows():
            quality = row['信号质量']
            if quality == 'Good':
                color = 'lightgreen'
            elif quality == 'Fair':
                color = 'lightyellow'
            else:
                color = 'lightcoral'

            for j in range(len(df.columns)):
                table[(i+1, j)].set_facecolor(color)

        plt.title('MUSIC分析 - 通道概览表', fontsize=16, fontweight='bold', pad=20)
        plt.savefig(f"{save_dir}/channel_overview_table.png", dpi=300, bbox_inches='tight')
        plt.show()

        print(f"  ✅ 通道概览表: {save_dir}/channel_overview.csv")

    def _create_signal_quality_analysis(self, save_dir):
        """创建信号质量分析"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 收集所有通道的信号统计
        all_stats = []
        for channel_name, result in self.channel_results.items():
            stats = result['signal_stats'].copy()
            stats['channel'] = channel_name
            stats['anatomy'] = result['anatomy']
            all_stats.append(stats)

        df_stats = pd.DataFrame(all_stats)

        # 1. RMS分布
        ax = axes[0, 0]
        ax.bar(df_stats['channel'], df_stats['rms'], color='skyblue')
        ax.set_title('各通道RMS值', fontweight='bold')
        ax.set_ylabel('RMS')
        ax.tick_params(axis='x', rotation=45)

        # 2. 频带功率分布
        ax = axes[0, 1]
        freq_bands = ['delta_power', 'theta_power', 'alpha_power', 'beta_power', 'gamma_power']
        band_names = ['Delta', 'Theta', 'Alpha', 'Beta', 'Gamma']

        x = np.arange(len(df_stats))
        width = 0.15

        for i, (band, name) in enumerate(zip(freq_bands, band_names)):
            ax.bar(x + i*width, df_stats[band], width, label=name)

        ax.set_title('频带功率分布', fontweight='bold')
        ax.set_ylabel('功率')
        ax.set_xticks(x + width*2)
        ax.set_xticklabels(df_stats['channel'], rotation=45)
        ax.legend()

        # 3. 主导频率
        ax = axes[0, 2]
        ax.scatter(df_stats['channel'], df_stats['dominant_freq'], c='red', s=50)
        ax.set_title('各通道主导频率', fontweight='bold')
        ax.set_ylabel('频率 (Hz)')
        ax.tick_params(axis='x', rotation=45)

        # 4. MUSIC强度分布
        ax = axes[1, 0]
        music_strengths = [self.channel_statistics[ch]['max_music_strength']
                          for ch in df_stats['channel']]
        ax.bar(df_stats['channel'], music_strengths, color='gold')
        ax.set_title('MUSIC最大强度', fontweight='bold')
        ax.set_ylabel('MUSIC强度')
        ax.tick_params(axis='x', rotation=45)

        # 5. 最优源数分布
        ax = axes[1, 1]
        optimal_sources = [self.channel_statistics[ch]['optimal_n_sources']
                          for ch in df_stats['channel']]
        ax.bar(df_stats['channel'], optimal_sources, color='lightcoral')
        ax.set_title('各通道最优源数', fontweight='bold')
        ax.set_ylabel('源数')
        ax.tick_params(axis='x', rotation=45)

        # 6. 信号质量分布
        ax = axes[1, 2]
        quality_counts = {}
        for ch in df_stats['channel']:
            quality = self.channel_statistics[ch]['signal_quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1

        colors = {'Good': 'green', 'Fair': 'orange', 'Poor': 'red'}
        qualities = list(quality_counts.keys())
        counts = list(quality_counts.values())
        bar_colors = [colors.get(q, 'gray') for q in qualities]

        ax.bar(qualities, counts, color=bar_colors)
        ax.set_title('信号质量分布', fontweight='bold')
        ax.set_ylabel('通道数')

        plt.tight_layout()
        plt.savefig(f"{save_dir}/signal_quality_analysis.png", dpi=300, bbox_inches='tight')
        plt.show()

        print(f"  ✅ 信号质量分析: {save_dir}/signal_quality_analysis.png")

    def _create_source_localization_visualization(self, save_dir):
        """创建源定位结果可视化"""
        fig, axes = plt.subplots(4, 5, figsize=(25, 20))
        axes = axes.flatten()

        for i, (channel_name, result) in enumerate(self.channel_results.items()):
            if i >= len(axes):
                break

            ax = axes[i]

            # 获取最优MUSIC结果
            optimal_n = result['optimal_sources']
            if optimal_n in result['music_results']:
                music_result = result['music_results'][optimal_n]
                if music_result['success']:
                    spectrum = music_result['spectrum']
                    positions = self.source_positions

                    # 3D散点图投影到2D
                    scatter = ax.scatter(positions[:, 0], positions[:, 1],
                                       c=spectrum, cmap='hot', s=1, alpha=0.7)

                    # 标记峰值源
                    for j, peak in enumerate(result['peak_sources'][:3]):  # 只显示前3个峰值
                        pos = peak['position']
                        ax.scatter(pos[0], pos[1], c='blue', s=50, marker='*')
                        ax.annotate(f"{j+1}",
                                  (pos[0], pos[1]), xytext=(5, 5),
                                  textcoords='offset points', fontsize=8, color='blue')

            ax.set_title(f"{channel_name}\n({result['anatomy']})", fontsize=10)
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')

        # 隐藏多余的子图
        for i in range(len(self.channel_results), len(axes)):
            axes[i].set_visible(False)

        plt.suptitle('MUSIC源定位结果 - 各通道XY平面投影', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f"{save_dir}/source_localization_all_channels.png", dpi=300, bbox_inches='tight')
        plt.show()

        print(f"  ✅ 源定位可视化: {save_dir}/source_localization_all_channels.png")

    def print_detailed_results(self):
        """打印详细分析结果"""
        print("\n" + "="*80)
        print("🎼 MUSIC逐通道详细分析结果")
        print("="*80)

        # 按MUSIC强度排序
        sorted_channels = sorted(self.channel_statistics.items(),
                               key=lambda x: x[1]['max_music_strength'],
                               reverse=True)

        for i, (channel_name, stats) in enumerate(sorted_channels):
            result = self.channel_results[channel_name]

            print(f"\n🎯 排名 #{i+1}: {channel_name} ({stats['anatomy']})")
            print(f"   {'='*50}")

            # 基本信息
            print(f"   📊 MUSIC强度: {stats['max_music_strength']:.4f}")
            print(f"   🎯 最优源数: {stats['optimal_n_sources']}")
            print(f"   ⭐ 峰值源数: {stats['n_peak_sources']}")
            print(f"   🏥 信号质量: {stats['signal_quality']}")
            print(f"   🧠 主导源区域: {stats['dominant_source_region']}")

            # 信号特性
            signal_stats = result['signal_stats']
            print(f"   📈 信号特性:")
            print(f"      RMS: {signal_stats['rms']:.4f}")
            print(f"      主导频率: {signal_stats['dominant_freq']:.1f} Hz")
            print(f"      Alpha功率: {signal_stats['alpha_power']:.4f}")
            print(f"      Beta功率: {signal_stats['beta_power']:.4f}")

            # 峰值源详情
            if result['peak_sources']:
                print(f"   🎯 峰值源详情:")
                for j, peak in enumerate(result['peak_sources'][:3]):
                    pos = peak['position']
                    print(f"      源{j+1}: 强度{peak['strength']:.3f}, "
                          f"位置({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), "
                          f"区域: {peak['anatomical_region']}")

        # 总体统计
        print(f"\n" + "="*80)
        print("📊 总体统计")
        print("="*80)

        all_strengths = [stats['max_music_strength'] for stats in self.channel_statistics.values()]
        all_sources = [stats['optimal_n_sources'] for stats in self.channel_statistics.values()]

        print(f"   平均MUSIC强度: {np.mean(all_strengths):.4f} ± {np.std(all_strengths):.4f}")
        print(f"   最大MUSIC强度: {np.max(all_strengths):.4f} ({sorted_channels[0][0]})")
        print(f"   平均最优源数: {np.mean(all_sources):.1f}")

        # 信号质量统计
        quality_counts = {}
        for stats in self.channel_statistics.values():
            quality = stats['signal_quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1

        print(f"   信号质量分布:")
        for quality, count in quality_counts.items():
            percentage = count / len(self.channel_statistics) * 100
            print(f"      {quality}: {count}通道 ({percentage:.1f}%)")

        # 解剖区域分析
        region_strengths = {}
        for stats in self.channel_statistics.values():
            region = stats['dominant_source_region']
            if region != 'None':
                if region not in region_strengths:
                    region_strengths[region] = []
                region_strengths[region].append(stats['max_music_strength'])

        if region_strengths:
            print(f"   主要激活区域:")
            sorted_regions = sorted(region_strengths.items(),
                                  key=lambda x: np.mean(x[1]), reverse=True)
            for region, strengths in sorted_regions[:5]:
                print(f"      {region}: 平均强度{np.mean(strengths):.4f} ({len(strengths)}通道)")

    def save_results(self, save_dir='music_analysis_results'):
        """保存分析结果"""
        import os
        import json
        os.makedirs(save_dir, exist_ok=True)

        # 保存详细结果
        with open(f"{save_dir}/detailed_results.json", 'w', encoding='utf-8') as f:
            # 转换numpy数组为列表以便JSON序列化
            serializable_results = {}
            for channel, result in self.channel_results.items():
                serializable_result = {}
                for key, value in result.items():
                    if isinstance(value, np.ndarray):
                        serializable_result[key] = value.tolist()
                    elif isinstance(value, dict):
                        serializable_result[key] = {}
                        for k, v in value.items():
                            if isinstance(v, np.ndarray):
                                serializable_result[key][k] = v.tolist()
                            else:
                                serializable_result[key][k] = v
                    else:
                        serializable_result[key] = value
                serializable_results[channel] = serializable_result

            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        # 保存统计结果
        with open(f"{save_dir}/statistics.json", 'w', encoding='utf-8') as f:
            json.dump(self.channel_statistics, f, indent=2, ensure_ascii=False)

        print(f"📁 详细结果已保存到: {save_dir}/")


def main():
    """主函数"""
    import os

    print("🎼 MUSIC算法逐通道EEG源定位分析")
    print("="*60)

    # 检查必要文件
    required_files = ['signal-1.csv.gz', 'hdbet_bem_output']
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")

        if 'hdbet_bem_output' in missing_files:
            print("\n请先运行BEM模型构建:")
            print("   python advanced_main.py sub-1_T1w.nii --method hd-bet")

        return

    try:
        # 创建分析器
        analyzer = MUSICChannelAnalyzer()

        # 运行分析
        if analyzer.analyze_all_channels():
            # 打印详细结果
            analyzer.print_detailed_results()

            # 创建综合报告
            analyzer.create_comprehensive_report()

            # 保存结果
            analyzer.save_results()

            print(f"\n🎉 MUSIC逐通道分析完成!")
            print(f"📊 结果已保存到: music_analysis_results/")
        else:
            print("❌ 分析失败")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
