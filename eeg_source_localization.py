"""
EEG源定位系统
基于HD-BET 5层BEM模型进行多种方法的EEG源定位
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
from mne import make_forward_solution, make_bem_solution, make_bem_model
from mne.minimum_norm import make_inverse_operator, apply_inverse
from mne.beamformer import make_lcmv, apply_lcmv
import nibabel as nib
from scipy import linalg
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class EEGSourceLocalizer:
    """EEG源定位器"""
    
    def __init__(self, bem_output_dir='hdbet_bem_output', eeg_file='signal-1.csv.gz'):
        """
        初始化EEG源定位器
        
        Parameters:
        -----------
        bem_output_dir : str
            BEM模型输出目录
        eeg_file : str
            EEG信号文件路径
        """
        self.bem_output_dir = bem_output_dir
        self.eeg_file = eeg_file
        
        # 数据存储
        self.eeg_data = None
        self.eeg_info = None
        self.bem_model = None
        self.bem_solution = None
        self.forward_model = None
        self.source_space = None
        self.inverse_operators = {}
        self.source_estimates = {}
        
        # 标准10-20电极位置 (简化版本)
        self.electrode_positions = self._get_standard_electrode_positions()
        
        print("EEG源定位系统初始化完成")
        print(f"BEM模型目录: {bem_output_dir}")
        print(f"EEG信号文件: {eeg_file}")
    
    def _get_standard_electrode_positions(self):
        """获取标准10-20电极位置"""
        # 这里使用简化的标准10-20系统电极位置
        # 实际应用中应该使用精确的电极位置文件
        positions = {
            'Fp1': [-0.0308, 0.0954, 0.0062],
            'Fp2': [0.0308, 0.0954, 0.0062],
            'F7': [-0.0761, 0.0578, 0.0057],
            'F3': [-0.0545, 0.0676, 0.0645],
            'Fz': [0.0000, 0.0676, 0.0676],
            'F4': [0.0545, 0.0676, 0.0645],
            'F8': [0.0761, 0.0578, 0.0057],
            'T7': [-0.0900, 0.0000, 0.0000],
            'C3': [-0.0636, 0.0000, 0.0636],
            'Cz': [0.0000, 0.0000, 0.0900],
            'C4': [0.0636, 0.0000, 0.0636],
            'T8': [0.0900, 0.0000, 0.0000],
            'P7': [-0.0761, -0.0578, 0.0057],
            'P3': [-0.0545, -0.0676, 0.0645],
            'Pz': [0.0000, -0.0676, 0.0676],
            'P4': [0.0545, -0.0676, 0.0645],
            'P8': [0.0761, -0.0578, 0.0057],
            'O1': [-0.0308, -0.0954, 0.0062],
            'O2': [0.0308, -0.0954, 0.0062]
        }
        return positions
    
    def load_eeg_data(self):
        """加载EEG数据"""
        print("正在加载EEG数据...")
        
        try:
            # 读取压缩的CSV文件
            df = pd.read_csv(self.eeg_file, compression='gzip')
            
            print(f"EEG数据形状: {df.shape}")
            print(f"列数: {len(df.columns)}")
            
            # 分析数据结构
            # 从数据来看，前面的列是EEG信号，后面的列可能是其他信息
            # 假设前19列是EEG通道数据（标准10-20系统）
            n_channels = 19
            eeg_columns = df.columns[:n_channels]
            
            # 提取EEG信号数据
            eeg_signals = df[eeg_columns].values.T  # 转置为 (n_channels, n_times)
            
            # 创建MNE Info对象
            ch_names = list(self.electrode_positions.keys())[:n_channels]
            sfreq = 250.0  # 假设采样率为250Hz，实际应根据数据确定
            
            info = mne.create_info(
                ch_names=ch_names,
                sfreq=sfreq,
                ch_types='eeg'
            )
            
            # 创建Raw对象
            self.eeg_data = mne.io.RawArray(eeg_signals, info)
            self.eeg_info = info
            
            # 设置电极位置
            montage = mne.channels.make_dig_montage(
                ch_pos={ch: pos for ch, pos in self.electrode_positions.items() 
                        if ch in ch_names},
                coord_frame='head'
            )
            self.eeg_data.set_montage(montage)
            
            print(f"成功加载EEG数据:")
            print(f"  通道数: {len(ch_names)}")
            print(f"  采样点数: {eeg_signals.shape[1]}")
            print(f"  采样率: {sfreq} Hz")
            print(f"  时长: {eeg_signals.shape[1]/sfreq:.1f} 秒")
            
            return True
            
        except Exception as e:
            print(f"加载EEG数据失败: {e}")
            return False
    
    def load_bem_model(self):
        """加载BEM模型"""
        print("正在加载BEM模型...")
        
        try:
            # 加载表面数据
            surfaces = []
            surface_names = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
            
            for surface_name in surface_names:
                surface_file = f"{self.bem_output_dir}/{surface_name}_surface.npz"
                try:
                    data = np.load(surface_file)
                    vertices = data['vertices'] / 1000.0  # 转换为米
                    faces = data['faces']
                    
                    # 创建MNE表面格式
                    surface = {
                        'rr': vertices,
                        'tris': faces,
                        'ntri': len(faces),
                        'np': len(vertices),
                        'coord_frame': mne.io.constants.FIFF.FIFFV_COORD_MRI
                    }
                    
                    surfaces.append(surface)
                    print(f"  加载{surface_name}: {len(vertices)}顶点, {len(faces)}面")
                    
                except FileNotFoundError:
                    print(f"  警告: 未找到{surface_name}表面文件")
                    continue
            
            if len(surfaces) == 0:
                print("错误: 没有找到有效的表面文件")
                return False
            
            # 创建BEM模型
            self.bem_model = surfaces
            
            # 设置电导率
            conductivities = [0.33, 0.0042, 1.79, 0.33, 0.14][:len(surfaces)]
            
            # 创建BEM解
            self.bem_solution = {
                'surfaces': surfaces,
                'sigma': conductivities,
                'is_sphere': False,
                'coord_frame': mne.io.constants.FIFF.FIFFV_COORD_MRI
            }
            
            print(f"BEM模型加载完成: {len(surfaces)}层")
            return True
            
        except Exception as e:
            print(f"加载BEM模型失败: {e}")
            return False
    
    def create_source_space(self):
        """创建源空间"""
        print("正在创建源空间...")
        
        try:
            # 使用灰质和白质表面创建源空间
            # 这里简化为使用体积源空间
            
            # 从分割结果创建源空间
            seg_file = f"{self.bem_output_dir}/segmentation.nii.gz"
            seg_img = nib.load(seg_file)
            seg_data = seg_img.get_fdata()
            
            # 获取脑组织区域 (灰质=4, 白质=5)
            brain_mask = (seg_data == 4) | (seg_data == 5)
            
            # 获取脑组织的体素坐标
            brain_coords = np.array(np.where(brain_mask)).T
            
            # 转换到物理坐标
            affine = seg_img.affine
            brain_coords_phys = nib.affines.apply_affine(affine, brain_coords)
            
            # 采样源点（避免源点过多）
            n_sources = min(5000, len(brain_coords_phys))
            if len(brain_coords_phys) > n_sources:
                indices = np.random.choice(len(brain_coords_phys), n_sources, replace=False)
                source_positions = brain_coords_phys[indices] / 1000.0  # 转换为米
            else:
                source_positions = brain_coords_phys / 1000.0
            
            # 创建源空间
            self.source_space = {
                'rr': source_positions,
                'nn': np.zeros_like(source_positions),  # 法向量，体积源不需要
                'inuse': np.ones(len(source_positions), dtype=bool),
                'nuse': len(source_positions),
                'coord_frame': mne.io.constants.FIFF.FIFFV_COORD_MRI
            }
            
            print(f"源空间创建完成: {len(source_positions)}个源点")
            return True
            
        except Exception as e:
            print(f"创建源空间失败: {e}")
            return False

    def compute_forward_model(self):
        """计算正向模型"""
        print("正在计算正向模型...")

        try:
            # 简化的正向建模
            n_channels = len(self.eeg_info['ch_names'])
            n_sources = self.source_space['nuse']

            # 获取电极位置
            electrode_pos = np.array([self.electrode_positions[ch]
                                    for ch in self.eeg_info['ch_names']])

            # 获取源位置
            source_pos = self.source_space['rr']

            # 计算导联场矩阵 (简化版本)
            leadfield = np.zeros((n_channels, n_sources * 3))  # 3个方向

            for i, elec_pos in enumerate(electrode_pos):
                for j, src_pos in enumerate(source_pos):
                    # 计算距离
                    r = np.linalg.norm(elec_pos - src_pos)
                    if r > 0:
                        # 简化的偶极子模型
                        for k in range(3):  # x, y, z方向
                            leadfield[i, j*3 + k] = 1.0 / (4 * np.pi * r**2)

            self.forward_model = {
                'sol': {'data': leadfield},
                'nsource': n_sources,
                'nchan': n_channels,
                'source_rr': source_pos,
                'source_nn': self.source_space['nn']
            }

            print(f"正向模型计算完成:")
            print(f"  导联场矩阵形状: {leadfield.shape}")
            print(f"  源点数: {n_sources}")
            print(f"  电极数: {n_channels}")

            return True

        except Exception as e:
            print(f"计算正向模型失败: {e}")
            return False

    def minimum_norm_estimation(self, lambda_reg=0.1):
        """最小范数估计 (MNE)"""
        print("正在进行最小范数估计...")

        try:
            # 获取EEG数据
            eeg_data = self.eeg_data.get_data()
            leadfield = self.forward_model['sol']['data']

            # 计算噪声协方差矩阵 (简化)
            noise_cov = np.eye(leadfield.shape[0]) * np.var(eeg_data)

            # 最小范数估计
            L = leadfield
            C_inv = np.linalg.inv(noise_cov)

            # 正则化参数
            n_sources = L.shape[1]
            reg_matrix = lambda_reg * np.eye(n_sources)

            # 计算逆算子
            LTC_inv = L.T @ C_inv
            inverse_operator = np.linalg.inv(LTC_inv @ L + reg_matrix) @ LTC_inv

            # 应用到数据
            source_activity = inverse_operator @ eeg_data

            # 计算源强度 (3个方向的模长)
            n_sources_actual = source_activity.shape[0] // 3
            source_strength = np.zeros((n_sources_actual, eeg_data.shape[1]))

            for i in range(n_sources_actual):
                x = source_activity[i*3, :]
                y = source_activity[i*3+1, :]
                z = source_activity[i*3+2, :]
                source_strength[i, :] = np.sqrt(x**2 + y**2 + z**2)

            self.source_estimates['MNE'] = {
                'data': source_strength,
                'vertices': self.source_space['rr'],
                'method': 'Minimum Norm Estimation',
                'lambda': lambda_reg
            }

            print(f"MNE完成: {source_strength.shape}")
            return True

        except Exception as e:
            print(f"最小范数估计失败: {e}")
            return False

    def sloreta(self, lambda_reg=0.1):
        """标准化低分辨率脑电磁断层成像 (sLORETA)"""
        print("正在进行sLORETA...")

        try:
            # 获取EEG数据
            eeg_data = self.eeg_data.get_data()
            leadfield = self.forward_model['sol']['data']

            # 计算噪声协方差矩阵
            noise_cov = np.eye(leadfield.shape[0]) * np.var(eeg_data)

            L = leadfield
            C_inv = np.linalg.inv(noise_cov)
            n_sources = L.shape[1]

            # sLORETA逆算子
            LTC_inv = L.T @ C_inv
            reg_matrix = lambda_reg * np.eye(n_sources)
            W = np.linalg.inv(LTC_inv @ L + reg_matrix) @ LTC_inv

            # 计算标准化矩阵
            S = W @ L

            # 标准化
            source_activity = np.zeros_like(W @ eeg_data)
            for i in range(n_sources):
                if S[i, i] > 0:
                    source_activity[i, :] = (W[i, :] @ eeg_data) / np.sqrt(S[i, i])
                else:
                    source_activity[i, :] = W[i, :] @ eeg_data

            # 计算源强度
            n_sources_actual = source_activity.shape[0] // 3
            source_strength = np.zeros((n_sources_actual, eeg_data.shape[1]))

            for i in range(n_sources_actual):
                x = source_activity[i*3, :]
                y = source_activity[i*3+1, :]
                z = source_activity[i*3+2, :]
                source_strength[i, :] = np.sqrt(x**2 + y**2 + z**2)

            self.source_estimates['sLORETA'] = {
                'data': source_strength,
                'vertices': self.source_space['rr'],
                'method': 'sLORETA',
                'lambda': lambda_reg
            }

            print(f"sLORETA完成: {source_strength.shape}")
            return True

        except Exception as e:
            print(f"sLORETA失败: {e}")
            return False

    def dspm(self, lambda_reg=0.1):
        """动态统计参数映射 (dSPM)"""
        print("正在进行dSPM...")

        try:
            # 获取EEG数据
            eeg_data = self.eeg_data.get_data()
            leadfield = self.forward_model['sol']['data']

            # 计算噪声协方差矩阵
            noise_cov = np.eye(leadfield.shape[0]) * np.var(eeg_data)

            L = leadfield
            C_inv = np.linalg.inv(noise_cov)
            n_sources = L.shape[1]

            # dSPM逆算子
            LTC_inv = L.T @ C_inv
            reg_matrix = lambda_reg * np.eye(n_sources)
            W = np.linalg.inv(LTC_inv @ L + reg_matrix) @ LTC_inv

            # 计算噪声标准化
            noise_norm = np.zeros(n_sources)
            for i in range(n_sources):
                noise_norm[i] = np.sqrt(W[i, :] @ noise_cov @ W[i, :].T)

            # 应用逆算子
            source_activity = W @ eeg_data

            # dSPM标准化
            for i in range(n_sources):
                if noise_norm[i] > 0:
                    source_activity[i, :] /= noise_norm[i]

            # 计算源强度
            n_sources_actual = source_activity.shape[0] // 3
            source_strength = np.zeros((n_sources_actual, eeg_data.shape[1]))

            for i in range(n_sources_actual):
                x = source_activity[i*3, :]
                y = source_activity[i*3+1, :]
                z = source_activity[i*3+2, :]
                source_strength[i, :] = np.sqrt(x**2 + y**2 + z**2)

            self.source_estimates['dSPM'] = {
                'data': source_strength,
                'vertices': self.source_space['rr'],
                'method': 'dSPM',
                'lambda': lambda_reg
            }

            print(f"dSPM完成: {source_strength.shape}")
            return True

        except Exception as e:
            print(f"dSPM失败: {e}")
            return False

    def music(self, n_sources_est=10):
        """多信号分类算法 (MUSIC)"""
        print("正在进行MUSIC...")

        try:
            # 获取EEG数据
            eeg_data = self.eeg_data.get_data()
            leadfield = self.forward_model['sol']['data']

            # 计算数据协方差矩阵
            data_cov = np.cov(eeg_data)

            # 特征值分解
            eigenvals, eigenvecs = np.linalg.eigh(data_cov)

            # 按特征值降序排列
            idx = np.argsort(eigenvals)[::-1]
            eigenvals = eigenvals[idx]
            eigenvecs = eigenvecs[:, idx]

            # 噪声子空间
            noise_subspace = eigenvecs[:, n_sources_est:]

            # MUSIC伪谱
            n_sources_actual = leadfield.shape[1] // 3
            music_spectrum = np.zeros(n_sources_actual)

            for i in range(n_sources_actual):
                # 获取该源点的导联场向量
                L_i = leadfield[:, i*3:(i+1)*3]

                # 计算MUSIC伪谱
                for j in range(3):  # 对每个方向
                    l = L_i[:, j]
                    if np.linalg.norm(l) > 0:
                        l = l / np.linalg.norm(l)  # 标准化

                        # MUSIC伪谱计算
                        proj = noise_subspace @ noise_subspace.T @ l
                        music_spectrum[i] += 1.0 / (l.T @ proj + 1e-10)

            # 标准化
            music_spectrum = music_spectrum / np.max(music_spectrum)

            # 扩展到时间维度（MUSIC给出空间分布）
            time_points = eeg_data.shape[1]
            source_strength = np.tile(music_spectrum[:, np.newaxis], (1, time_points))

            self.source_estimates['MUSIC'] = {
                'data': source_strength,
                'vertices': self.source_space['rr'],
                'method': 'MUSIC',
                'n_sources': n_sources_est
            }

            print(f"MUSIC完成: {source_strength.shape}")
            return True

        except Exception as e:
            print(f"MUSIC失败: {e}")
            return False

    def beamformer_lcmv(self):
        """线性约束最小方差波束形成器 (LCMV)"""
        print("正在进行LCMV波束形成...")

        try:
            # 获取EEG数据
            eeg_data = self.eeg_data.get_data()
            leadfield = self.forward_model['sol']['data']

            # 计算数据协方差矩阵
            data_cov = np.cov(eeg_data)

            # 正则化
            reg_param = 0.01 * np.trace(data_cov) / data_cov.shape[0]
            data_cov_reg = data_cov + reg_param * np.eye(data_cov.shape[0])

            # 计算协方差矩阵的逆
            data_cov_inv = np.linalg.inv(data_cov_reg)

            # LCMV波束形成器权重
            n_sources_actual = leadfield.shape[1] // 3
            source_strength = np.zeros((n_sources_actual, eeg_data.shape[1]))

            for i in range(n_sources_actual):
                # 获取该源点的导联场向量
                L_i = leadfield[:, i*3:(i+1)*3]

                # LCMV权重计算
                try:
                    # 计算源协方差
                    source_cov = L_i.T @ data_cov_inv @ L_i
                    source_cov_inv = np.linalg.inv(source_cov + 1e-10 * np.eye(3))

                    # LCMV权重
                    W = data_cov_inv @ L_i @ source_cov_inv @ L_i.T @ data_cov_inv

                    # 应用权重
                    source_activity = W @ eeg_data

                    # 计算功率
                    power = np.mean(source_activity**2, axis=0)
                    source_strength[i, :] = np.sqrt(power)

                except np.linalg.LinAlgError:
                    # 如果矩阵奇异，使用简化方法
                    source_strength[i, :] = 0

            self.source_estimates['LCMV'] = {
                'data': source_strength,
                'vertices': self.source_space['rr'],
                'method': 'LCMV Beamformer',
                'reg_param': reg_param
            }

            print(f"LCMV完成: {source_strength.shape}")
            return True

        except Exception as e:
            print(f"LCMV失败: {e}")
            return False

    def run_all_methods(self):
        """运行所有源定位方法"""
        print("="*60)
        print("开始运行所有EEG源定位方法")
        print("="*60)

        # 1. 加载数据
        if not self.load_eeg_data():
            return False

        if not self.load_bem_model():
            return False

        if not self.create_source_space():
            return False

        if not self.compute_forward_model():
            return False

        # 2. 运行各种源定位方法
        methods = [
            ('MNE', self.minimum_norm_estimation),
            ('sLORETA', self.sloreta),
            ('dSPM', self.dspm),
            ('MUSIC', self.music),
            ('LCMV', self.beamformer_lcmv)
        ]

        successful_methods = []

        for method_name, method_func in methods:
            print(f"\n{'='*40}")
            print(f"运行 {method_name}")
            print(f"{'='*40}")

            try:
                if method_func():
                    successful_methods.append(method_name)
                    print(f"✅ {method_name} 成功完成")
                else:
                    print(f"❌ {method_name} 失败")
            except Exception as e:
                print(f"❌ {method_name} 异常: {e}")

        print(f"\n{'='*60}")
        print(f"源定位完成! 成功方法: {successful_methods}")
        print(f"{'='*60}")

        return len(successful_methods) > 0

    def visualize_results(self, time_point=None, save_dir='source_localization_results'):
        """可视化源定位结果"""
        import os
        os.makedirs(save_dir, exist_ok=True)

        if not self.source_estimates:
            print("没有源定位结果可显示")
            return

        # 选择时间点
        if time_point is None:
            # 选择信号能量最大的时间点
            eeg_data = self.eeg_data.get_data()
            power = np.mean(eeg_data**2, axis=0)
            time_point = np.argmax(power)

        print(f"可视化时间点: {time_point}")

        # 创建子图
        n_methods = len(self.source_estimates)
        fig = plt.figure(figsize=(20, 4*n_methods))

        for i, (method_name, result) in enumerate(self.source_estimates.items()):
            # 获取该时间点的源强度
            source_strength = result['data'][:, time_point]
            vertices = result['vertices']

            # 2D投影可视化
            ax = plt.subplot(n_methods, 3, i*3 + 1)
            scatter = ax.scatter(vertices[:, 0], vertices[:, 1],
                               c=source_strength, cmap='hot', s=1, alpha=0.7)
            ax.set_title(f'{method_name} - XY平面')
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            plt.colorbar(scatter, ax=ax)

            ax = plt.subplot(n_methods, 3, i*3 + 2)
            scatter = ax.scatter(vertices[:, 0], vertices[:, 2],
                               c=source_strength, cmap='hot', s=1, alpha=0.7)
            ax.set_title(f'{method_name} - XZ平面')
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Z (m)')
            plt.colorbar(scatter, ax=ax)

            ax = plt.subplot(n_methods, 3, i*3 + 3)
            scatter = ax.scatter(vertices[:, 1], vertices[:, 2],
                               c=source_strength, cmap='hot', s=1, alpha=0.7)
            ax.set_title(f'{method_name} - YZ平面')
            ax.set_xlabel('Y (m)')
            ax.set_ylabel('Z (m)')
            plt.colorbar(scatter, ax=ax)

        plt.tight_layout()
        plt.savefig(f'{save_dir}/source_localization_comparison.png',
                   dpi=300, bbox_inches='tight')
        plt.show()

        print(f"可视化结果已保存到: {save_dir}/source_localization_comparison.png")

    def analyze_results(self):
        """分析源定位结果"""
        if not self.source_estimates:
            print("没有源定位结果可分析")
            return

        print("\n" + "="*60)
        print("源定位结果分析")
        print("="*60)

        for method_name, result in self.source_estimates.items():
            source_strength = result['data']
            vertices = result['vertices']

            # 统计信息
            max_strength = np.max(source_strength)
            mean_strength = np.mean(source_strength)
            std_strength = np.std(source_strength)

            # 找到最强的源点
            max_idx = np.unravel_index(np.argmax(source_strength), source_strength.shape)
            max_source_pos = vertices[max_idx[0]]
            max_time = max_idx[1]

            print(f"\n{method_name}:")
            print(f"  最大强度: {max_strength:.6f}")
            print(f"  平均强度: {mean_strength:.6f}")
            print(f"  标准差: {std_strength:.6f}")
            print(f"  最强源位置: ({max_source_pos[0]:.3f}, {max_source_pos[1]:.3f}, {max_source_pos[2]:.3f}) m")
            print(f"  最强源时间: {max_time} ({max_time/250.0:.3f} s)")

        # 方法间比较
        print(f"\n{'='*40}")
        print("方法比较:")
        print(f"{'='*40}")

        max_strengths = {}
        for method_name, result in self.source_estimates.items():
            max_strengths[method_name] = np.max(result['data'])

        # 按最大强度排序
        sorted_methods = sorted(max_strengths.items(), key=lambda x: x[1], reverse=True)

        for i, (method, strength) in enumerate(sorted_methods):
            print(f"{i+1}. {method}: {strength:.6f}")

        return sorted_methods
