#!/usr/bin/env python3
"""
HD-BET结果专门可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as mpatches

def visualize_hdbet_results():
    """可视化HD-BET结果"""
    print("🧠 HD-BET深度学习分割结果可视化")
    print("="*50)
    
    # 加载HD-BET分割结果
    try:
        seg_img = nib.load('hdbet_bem_output/segmentation.nii.gz')
        seg_data = seg_img.get_fdata()
        print(f"✅ 成功加载HD-BET分割结果")
        print(f"   数据形状: {seg_data.shape}")
    except Exception as e:
        print(f"❌ 加载HD-BET分割结果失败: {e}")
        return
    
    # 加载原始MRI数据进行对比
    try:
        mri_img = nib.load('sub-1_T1w.nii')
        mri_data = mri_img.get_fdata()
        print(f"✅ 成功加载原始MRI数据")
    except Exception as e:
        print(f"❌ 加载原始MRI数据失败: {e}")
        return
    
    # 创建可视化
    fig = plt.figure(figsize=(20, 15))
    
    # 获取中心切片
    center_x = seg_data.shape[0] // 2
    center_y = seg_data.shape[1] // 2
    center_z = seg_data.shape[2] // 2
    
    # 组织颜色映射
    colors = ['black', 'pink', 'white', 'lightblue', 'gray', 'lightgray']
    tissue_names = ['背景', '头皮', '颅骨', '脑脊液', '灰质', '白质']
    
    # 1. 原始MRI - 三个视图
    ax1 = plt.subplot(3, 4, 1)
    plt.imshow(mri_data[center_x, :, :], cmap='gray')
    plt.title('原始MRI - 矢状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    ax2 = plt.subplot(3, 4, 2)
    plt.imshow(mri_data[:, center_y, :], cmap='gray')
    plt.title('原始MRI - 冠状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    ax3 = plt.subplot(3, 4, 3)
    plt.imshow(mri_data[:, :, center_z], cmap='gray')
    plt.title('原始MRI - 轴状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    # 2. HD-BET分割结果 - 三个视图
    ax4 = plt.subplot(3, 4, 5)
    im1 = plt.imshow(seg_data[center_x, :, :], cmap='tab10', vmin=0, vmax=5)
    plt.title('HD-BET分割 - 矢状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    ax5 = plt.subplot(3, 4, 6)
    im2 = plt.imshow(seg_data[:, center_y, :], cmap='tab10', vmin=0, vmax=5)
    plt.title('HD-BET分割 - 冠状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    ax6 = plt.subplot(3, 4, 7)
    im3 = plt.imshow(seg_data[:, :, center_z], cmap='tab10', vmin=0, vmax=5)
    plt.title('HD-BET分割 - 轴状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    # 3. 叠加显示
    ax7 = plt.subplot(3, 4, 9)
    plt.imshow(mri_data[center_x, :, :], cmap='gray', alpha=0.7)
    plt.imshow(seg_data[center_x, :, :], cmap='tab10', alpha=0.5, vmin=0, vmax=5)
    plt.title('叠加显示 - 矢状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    ax8 = plt.subplot(3, 4, 10)
    plt.imshow(mri_data[:, center_y, :], cmap='gray', alpha=0.7)
    plt.imshow(seg_data[:, center_y, :], cmap='tab10', alpha=0.5, vmin=0, vmax=5)
    plt.title('叠加显示 - 冠状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    ax9 = plt.subplot(3, 4, 11)
    plt.imshow(mri_data[:, :, center_z], cmap='gray', alpha=0.7)
    plt.imshow(seg_data[:, :, center_z], cmap='tab10', alpha=0.5, vmin=0, vmax=5)
    plt.title('叠加显示 - 轴状面', fontsize=12, fontweight='bold')
    plt.axis('off')
    
    # 4. 统计信息
    ax10 = plt.subplot(3, 4, 4)
    ax10.axis('off')
    
    # 计算统计信息
    total_voxels = seg_data.size
    stats_text = "HD-BET分割统计:\n\n"
    
    for label, name in enumerate(tissue_names):
        count = np.sum(seg_data == label)
        percentage = (count / total_voxels) * 100
        stats_text += f"{name}: {count:,} ({percentage:.1f}%)\n"
    
    # 脑组织总计
    brain_voxels = np.sum((seg_data == 4) | (seg_data == 5))
    brain_percentage = (brain_voxels / total_voxels) * 100
    stats_text += f"\n脑组织总计: {brain_voxels:,} ({brain_percentage:.1f}%)"
    
    ax10.text(0.1, 0.9, stats_text, transform=ax10.transAxes, 
              fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    # 5. 表面统计
    ax11 = plt.subplot(3, 4, 8)
    ax11.axis('off')
    
    surface_stats = load_surface_stats()
    surface_text = "表面网格统计:\n\n"
    
    total_vertices = 0
    total_faces = 0
    
    for surface, stats in surface_stats.items():
        if stats['vertices'] > 0:
            surface_text += f"{surface}:\n"
            surface_text += f"  {stats['vertices']:,} 顶点\n"
            surface_text += f"  {stats['faces']:,} 面\n\n"
            total_vertices += stats['vertices']
            total_faces += stats['faces']
    
    surface_text += f"总计:\n"
    surface_text += f"  {total_vertices:,} 顶点\n"
    surface_text += f"  {total_faces:,} 面"
    
    ax11.text(0.1, 0.9, surface_text, transform=ax11.transAxes,
              fontsize=9, verticalalignment='top', fontfamily='monospace')
    
    # 6. 3D可视化预览
    ax12 = plt.subplot(3, 4, 12, projection='3d')
    
    # 创建3D点云预览（采样显示）
    brain_mask = (seg_data == 4) | (seg_data == 5)  # 灰质+白质
    z, y, x = np.where(brain_mask)
    
    # 采样显示（避免点太多）
    if len(x) > 5000:
        indices = np.random.choice(len(x), 5000, replace=False)
        x, y, z = x[indices], y[indices], z[indices]
    
    ax12.scatter(x, y, z, c='red', s=0.1, alpha=0.6)
    ax12.set_title('脑组织3D分布', fontsize=10, fontweight='bold')
    ax12.set_xlabel('X')
    ax12.set_ylabel('Y')
    ax12.set_zlabel('Z')
    
    # 添加图例
    legend_elements = [mpatches.Patch(color=plt.cm.tab10(i), label=name) 
                      for i, name in enumerate(tissue_names)]
    fig.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
    
    plt.suptitle('HD-BET深度学习分割结果可视化', fontsize=16, fontweight='bold', y=0.95)
    plt.tight_layout()
    plt.savefig('hdbet_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n📊 HD-BET可视化结果已保存为: hdbet_visualization.png")

def load_surface_stats():
    """加载表面统计信息"""
    surfaces = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
    stats = {}
    
    for surface in surfaces:
        surface_file = f'hdbet_bem_output/{surface}_surface.npz'
        try:
            data = np.load(surface_file)
            stats[surface] = {
                'vertices': len(data['vertices']),
                'faces': len(data['faces'])
            }
        except:
            stats[surface] = {'vertices': 0, 'faces': 0}
    
    return stats

def compare_with_traditional():
    """与传统方法比较"""
    print("\n🔄 HD-BET vs 传统方法比较")
    print("="*40)
    
    methods = {
        'HD-BET': 'hdbet_bem_output/segmentation.nii.gz',
        '传统方法': 'traditional_bem_output/segmentation.nii.gz'
    }
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    for i, (method_name, seg_file) in enumerate(methods.items()):
        try:
            seg_img = nib.load(seg_file)
            seg_data = seg_img.get_fdata()
            
            center_x = seg_data.shape[0] // 2
            center_y = seg_data.shape[1] // 2
            center_z = seg_data.shape[2] // 2
            
            # 矢状面
            axes[i, 0].imshow(seg_data[center_x, :, :], cmap='tab10', vmin=0, vmax=5)
            axes[i, 0].set_title(f'{method_name} - 矢状面')
            axes[i, 0].axis('off')
            
            # 冠状面
            axes[i, 1].imshow(seg_data[:, center_y, :], cmap='tab10', vmin=0, vmax=5)
            axes[i, 1].set_title(f'{method_name} - 冠状面')
            axes[i, 1].axis('off')
            
            # 轴状面
            axes[i, 2].imshow(seg_data[:, :, center_z], cmap='tab10', vmin=0, vmax=5)
            axes[i, 2].set_title(f'{method_name} - 轴状面')
            axes[i, 2].axis('off')
            
            # 计算脑组织比例
            brain_voxels = np.sum((seg_data == 4) | (seg_data == 5))
            brain_percentage = (brain_voxels / seg_data.size) * 100
            print(f"{method_name}: 脑组织比例 {brain_percentage:.1f}%")
            
        except Exception as e:
            print(f"❌ 加载{method_name}失败: {e}")
    
    plt.tight_layout()
    plt.savefig('hdbet_vs_traditional.png', dpi=200, bbox_inches='tight')
    plt.show()
    
    print(f"📊 比较结果已保存为: hdbet_vs_traditional.png")

if __name__ == "__main__":
    visualize_hdbet_results()
    compare_with_traditional()
    print("\n🎉 HD-BET结果可视化完成!")
