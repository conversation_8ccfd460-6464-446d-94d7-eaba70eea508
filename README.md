# 5层BEM模型构建器 (集成开源深度学习模型)

这个项目实现了从MRI数据构建5层边界元方法(BEM)模型的完整流程，**集成了多个开源深度学习分割模型**以获得更高精度。BEM模型包含以下5层组织：

1. **头皮 (Scalp)** - 电导率: 0.33 S/m
2. **颅骨 (Skull)** - 电导率: 0.0042 S/m  
3. **脑脊液 (CSF)** - 电导率: 1.79 S/m
4. **灰质 (Gray Matter)** - 电导率: 0.33 S/m
5. **白质 (White Matter)** - 电导率: 0.14 S/m

## 🚀 主要特性

### 🧠 多种分割方法
- **SynthSeg**: 对比度无关的深度学习分割，支持任何MRI序列
- **FastSurfer**: FreeSurfer的深度学习版本，速度提升100倍
- **HD-BET**: 专业的脑提取深度学习工具
- **改进传统方法**: 基于强度阈值的增强分割算法

### 🔧 核心功能
- **MRI数据处理**: 支持.nii和.nii.gz格式的MRI文件
- **高精度组织分割**: 集成多个开源深度学习模型
- **表面提取**: 使用Marching Cubes算法提取3D表面
- **BEM模型构建**: 创建多层BEM几何模型
- **3D可视化**: 支持PyVista的交互式3D可视化
- **结果导出**: 保存表面网格、BEM解和分割结果

## 📦 安装

### 1. 基础安装

```bash
# 安装基础依赖
pip install -r requirements.txt
```

### 2. 安装开源分割模型 (推荐)

```bash
# 自动安装所有模型
python install_segmentation_models.py all

# 或单独安装
python install_segmentation_models.py synthseg    # SynthSeg
python install_segmentation_models.py fastsurfer  # FastSurfer
python install_segmentation_models.py hdbet       # HD-BET
```

### 3. 设置环境

```bash
# 设置环境变量
source setup_environment.sh
```

### 依赖包说明
- **基础包**: `mne`, `nibabel`, `numpy`, `scipy`, `matplotlib`, `pyvista`
- **深度学习**: `tensorflow` (SynthSeg), `torch` (FastSurfer), `HD-BET`
- **图像处理**: `scikit-image`, `trimesh`

## 🎯 使用方法

### 1. 高精度模式 (推荐)

```bash
# 自动选择最佳分割方法
python advanced_main.py sub-1_T1w.nii --method auto

# 使用SynthSeg (对比度无关)
python advanced_main.py sub-1_T1w.nii --method synthseg

# 使用FastSurfer (快速FreeSurfer)
python advanced_main.py sub-1_T1w.nii --method fastsurfer

# 使用HD-BET (专业脑提取)
python advanced_main.py sub-1_T1w.nii --method hd-bet

# 检查可用模型
python advanced_main.py sub-1_T1w.nii --check-only
```

### 2. 传统模式

```bash
# 使用改进的传统分割算法
python main.py sub-1_T1w.nii

# 启用3D可视化
python main.py sub-1_T1w.nii --visualize
```

### 2. Python API使用

```python
from bem_model_builder import BEMModelBuilder

# 创建构建器
builder = BEMModelBuilder("sub-1_T1w.nii")

# 完整处理流程
builder.load_mri_data()
builder.preprocess_mri()
builder.segment_tissues()
builder.extract_surfaces()
builder.build_bem_model()

# 保存结果
builder.save_model("output_directory")

# 可视化
builder.visualize_model()

# 获取模型信息
info = builder.get_model_info()
print(info)
```

### 3. 运行示例

如果当前目录中有MRI文件，直接运行：

```bash
python main.py
```

## 输出文件

构建完成后，输出目录将包含：

- `scalp_surface.npz`: 头皮表面网格
- `skull_surface.npz`: 颅骨表面网格  
- `csf_surface.npz`: 脑脊液表面网格
- `gray_matter_surface.npz`: 灰质表面网格
- `white_matter_surface.npz`: 白质表面网格
- `bem_solution.npz`: BEM解参数
- `segmentation.nii.gz`: 组织分割结果

## 处理流程

1. **数据加载**: 读取MRI文件并提取体数据
2. **预处理**: 标准化强度值，高斯滤波去噪
3. **组织分割**: 基于强度阈值识别不同组织类型
4. **表面提取**: 使用Marching Cubes算法生成3D表面网格
5. **网格简化**: 减少顶点数量以提高计算效率
6. **BEM构建**: 创建多层BEM几何模型
7. **求解**: 计算BEM解（简化版本）
8. **可视化**: 生成3D模型可视化

## 注意事项

1. **分割算法**: 当前使用简化的基于阈值的分割方法。对于更精确的结果，建议使用FreeSurfer等专业工具进行预分割。

2. **计算资源**: 高分辨率MRI数据可能需要大量内存和计算时间。

3. **坐标系**: 模型使用MRI原生坐标系，可能需要根据具体应用进行坐标变换。

4. **电导率值**: 使用的是文献中的典型值，实际应用中可能需要根据具体情况调整。

## 扩展功能

- 集成FreeSurfer进行更精确的组织分割
- 实现完整的BEM求解算法
- 添加更多可视化选项
- 支持更多MRI格式
- 添加质量检查和验证功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
