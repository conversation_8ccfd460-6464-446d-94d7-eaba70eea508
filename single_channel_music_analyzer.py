#!/usr/bin/env python3
"""
单通道MUSIC算法EEG源定位分析器
使用空间平滑和时间嵌入技术实现单通道MUSIC分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import linalg
from scipy.signal import find_peaks, welch
import nibabel as nib
from eeg_source_localization import EEGSourceLocalizer
import warnings
warnings.filterwarnings('ignore')

class SingleChannelMUSICAnalyzer:
    """单通道MUSIC算法分析器"""
    
    def __init__(self, bem_output_dir='hdbet_bem_output', eeg_file='signal-1.csv.gz'):
        """初始化分析器"""
        self.bem_output_dir = bem_output_dir
        self.eeg_file = eeg_file
        
        # 基础数据
        self.eeg_data = None
        self.channel_names = None
        self.sampling_rate = 250.0
        self.leadfield = None
        self.source_positions = None
        
        # 分析结果
        self.channel_results = {}
        self.channel_statistics = {}
        
        # 标准10-20电极位置和解剖对应
        self.electrode_anatomy = {
            'Fp1': '左前额极', 'Fp2': '右前额极', 'F7': '左前颞', 'F3': '左前额',
            'Fz': '正中前额', 'F4': '右前额', 'F8': '右前颞', 'T7': '左颞',
            'C3': '左中央', 'Cz': '正中中央', 'C4': '右中央', 'T8': '右颞',
            'P7': '左后颞', 'P3': '左顶', 'Pz': '正中顶', 'P4': '右顶',
            'P8': '右后颞', 'O1': '左枕', 'O2': '右枕'
        }
        
        print("单通道MUSIC分析器初始化完成")
    
    def load_data(self):
        """加载EEG数据和正向模型"""
        print("正在加载数据...")
        
        # 使用现有的源定位器加载数据
        localizer = EEGSourceLocalizer(self.bem_output_dir, self.eeg_file)
        
        # 加载所有必要数据
        if not localizer.load_eeg_data():
            return False
        if not localizer.load_bem_model():
            return False
        if not localizer.create_source_space():
            return False
        if not localizer.compute_forward_model():
            return False
        
        # 提取数据
        self.eeg_data = localizer.eeg_data.get_data()
        self.channel_names = localizer.eeg_info['ch_names']
        self.leadfield = localizer.forward_model['sol']['data']
        self.source_positions = localizer.source_space['rr']
        
        print(f"数据加载完成:")
        print(f"  通道数: {len(self.channel_names)}")
        print(f"  时间点: {self.eeg_data.shape[1]}")
        print(f"  源点数: {len(self.source_positions)}")
        
        return True
    
    def single_channel_music(self, channel_idx, n_sources=3, time_window=4.0, 
                           embedding_dim=20, overlap_ratio=0.5):
        """
        单通道MUSIC分析
        
        Parameters:
        -----------
        channel_idx : int
            通道索引
        n_sources : int
            估计源数
        time_window : float
            分析时间窗口(秒)
        embedding_dim : int
            时间嵌入维度
        overlap_ratio : float
            窗口重叠比例
        """
        channel_name = self.channel_names[channel_idx]
        channel_data = self.eeg_data[channel_idx, :]
        
        print(f"\n🎵 分析通道 {channel_name} ({self.electrode_anatomy.get(channel_name, '未知区域')})")
        
        # 1. 信号预处理和质量评估
        signal_stats = self._analyze_signal_quality(channel_data)
        print(f"  信号质量: {signal_stats['quality']}")
        print(f"  RMS: {signal_stats['rms']:.2f}, SNR估计: {signal_stats['snr_estimate']:.2f}")
        
        # 2. 选择最佳分析时间窗口
        window_samples = int(time_window * self.sampling_rate)
        best_window = self._select_optimal_window(channel_data, window_samples)
        
        window_data = channel_data[best_window[0]:best_window[1]]
        print(f"  分析窗口: {best_window[0]/self.sampling_rate:.2f}s - {best_window[1]/self.sampling_rate:.2f}s")
        
        # 3. 创建时间嵌入矩阵 (Hankel矩阵)
        embedded_matrix = self._create_hankel_matrix(window_data, embedding_dim)
        print(f"  嵌入矩阵形状: {embedded_matrix.shape}")
        
        # 4. 空间平滑 (使用相邻通道信息)
        smoothed_matrix = self._spatial_smoothing(channel_idx, best_window, embedding_dim)
        
        # 5. 计算协方差矩阵
        if smoothed_matrix is not None:
            cov_matrix = np.cov(smoothed_matrix)
            print(f"  使用空间平滑，协方差矩阵: {cov_matrix.shape}")
        else:
            cov_matrix = np.cov(embedded_matrix)
            print(f"  使用时间嵌入，协方差矩阵: {cov_matrix.shape}")
        
        # 添加正则化
        reg_param = 1e-6 * np.trace(cov_matrix) / cov_matrix.shape[0]
        cov_matrix_reg = cov_matrix + reg_param * np.eye(cov_matrix.shape[0])
        
        # 6. 特征值分解
        eigenvals, eigenvecs = linalg.eigh(cov_matrix_reg)
        
        # 按特征值降序排列
        idx = np.argsort(eigenvals)[::-1]
        eigenvals = eigenvals[idx]
        eigenvecs = eigenvecs[:, idx]
        
        # 7. 自动确定源数
        auto_n_sources = self._estimate_source_number(eigenvals)
        final_n_sources = min(n_sources, auto_n_sources, len(eigenvals)-2)
        
        print(f"  自动检测源数: {auto_n_sources}, 使用源数: {final_n_sources}")
        print(f"  特征值范围: {eigenvals[-1]:.2e} - {eigenvals[0]:.2e}")
        
        # 8. 子空间分解
        signal_subspace = eigenvecs[:, :final_n_sources]
        noise_subspace = eigenvecs[:, final_n_sources:]
        
        # 9. 计算MUSIC伪谱
        music_spectrum = self._compute_music_spectrum(
            channel_idx, noise_subspace, embedding_dim
        )
        
        # 10. 后处理
        music_spectrum_processed = self._postprocess_spectrum(music_spectrum)
        
        # 11. 峰值检测
        peaks = self._detect_peaks(music_spectrum_processed)
        
        # 12. 结果整理
        result = {
            'channel_name': channel_name,
            'channel_idx': channel_idx,
            'anatomy': self.electrode_anatomy.get(channel_name, '未知区域'),
            'signal_stats': signal_stats,
            'analysis_window': best_window,
            'embedding_dim': embedding_dim,
            'n_sources': final_n_sources,
            'auto_n_sources': auto_n_sources,
            'eigenvals': eigenvals,
            'music_spectrum_raw': music_spectrum,
            'music_spectrum': music_spectrum_processed,
            'peaks': peaks,
            'peak_positions': self.source_positions[peaks] if len(peaks) > 0 else np.array([]),
            'peak_strengths': music_spectrum_processed[peaks] if len(peaks) > 0 else np.array([]),
            'snr_estimate': eigenvals[0] / np.mean(eigenvals[final_n_sources:]) if final_n_sources < len(eigenvals) else 1.0
        }
        
        print(f"  ✅ 检测到 {len(peaks)} 个显著源")
        print(f"  最大MUSIC强度: {np.max(music_spectrum_processed):.4f}")
        
        return result
    
    def _analyze_signal_quality(self, signal):
        """分析信号质量"""
        # 基本统计
        rms = np.sqrt(np.mean(signal**2))
        std = np.std(signal)
        mean = np.mean(signal)
        
        # 频域分析
        freqs, psd = welch(signal, fs=self.sampling_rate, nperseg=1024)
        
        # 频带功率
        alpha_power = np.mean(psd[(freqs >= 8) & (freqs <= 13)])
        beta_power = np.mean(psd[(freqs >= 13) & (freqs <= 30)])
        gamma_power = np.mean(psd[(freqs >= 30) & (freqs <= 100)])
        total_power = np.sum(psd)
        
        # SNR估计 (信号功率 vs 高频噪声功率)
        signal_power = np.sum(psd[(freqs >= 1) & (freqs <= 40)])
        noise_power = np.sum(psd[(freqs >= 50) & (freqs <= 100)])
        snr_estimate = signal_power / (noise_power + 1e-10)
        
        # 质量评估
        if snr_estimate > 10 and rms < 50000:
            quality = 'Good'
        elif snr_estimate > 5 and rms < 100000:
            quality = 'Fair'
        else:
            quality = 'Poor'
        
        return {
            'rms': rms,
            'std': std,
            'mean': mean,
            'alpha_power': alpha_power,
            'beta_power': beta_power,
            'gamma_power': gamma_power,
            'total_power': total_power,
            'snr_estimate': snr_estimate,
            'quality': quality
        }
    
    def _select_optimal_window(self, signal, window_samples):
        """选择最佳分析窗口"""
        # 计算滑动窗口的功率
        step = window_samples // 4
        n_windows = (len(signal) - window_samples) // step + 1
        
        window_powers = []
        window_starts = []
        
        for i in range(n_windows):
            start = i * step
            end = start + window_samples
            if end <= len(signal):
                window_data = signal[start:end]
                # 计算信号功率和稳定性
                power = np.var(window_data)
                stability = 1.0 / (np.std(np.diff(window_data)) + 1e-10)
                score = power * stability
                
                window_powers.append(score)
                window_starts.append(start)
        
        # 选择最佳窗口
        if window_powers:
            best_idx = np.argmax(window_powers)
            best_start = window_starts[best_idx]
            return (best_start, best_start + window_samples)
        else:
            # 默认选择中间部分
            start = len(signal) // 2 - window_samples // 2
            return (start, start + window_samples)
    
    def _create_hankel_matrix(self, signal, embedding_dim):
        """创建Hankel矩阵进行时间嵌入"""
        n_samples = len(signal) - embedding_dim + 1
        if n_samples <= 0:
            embedding_dim = len(signal) // 2
            n_samples = len(signal) - embedding_dim + 1
        
        hankel_matrix = np.zeros((embedding_dim, n_samples))
        
        for i in range(embedding_dim):
            hankel_matrix[i, :] = signal[i:i+n_samples]
        
        return hankel_matrix
    
    def _spatial_smoothing(self, channel_idx, window, embedding_dim):
        """空间平滑：使用相邻通道信息"""
        # 定义通道邻接关系 (简化版本)
        channel_neighbors = {
            0: [1, 3, 4],      # Fp1 -> Fp2, F3, Fz
            1: [0, 4, 5],      # Fp2 -> Fp1, Fz, F4
            2: [3, 7],         # F7 -> F3, T7
            3: [0, 2, 4, 8],   # F3 -> Fp1, F7, Fz, C3
            4: [0, 1, 3, 5, 9], # Fz -> Fp1, Fp2, F3, F4, Cz
            5: [1, 4, 6, 10],  # F4 -> Fp2, Fz, F8, C4
            6: [5, 11],        # F8 -> F4, T8
            7: [2, 8],         # T7 -> F7, C3
            8: [3, 7, 9, 12],  # C3 -> F3, T7, Cz, P7
            9: [4, 8, 10, 13], # Cz -> Fz, C3, C4, Pz
            10: [5, 9, 11, 15], # C4 -> F4, Cz, T8, P4
            11: [6, 10],       # T8 -> F8, C4
            12: [8, 13],       # P7 -> C3, P3
            13: [9, 12, 14, 17], # P3 -> Cz, P7, Pz, O1
            14: [9, 13, 15, 17, 18], # Pz -> Cz, P3, P4, O1, O2
            15: [10, 14, 16],  # P4 -> C4, Pz, P8
            16: [15],          # P8 -> P4
            17: [13, 14, 18],  # O1 -> P3, Pz, O2
            18: [14, 17]       # O2 -> Pz, O1
        }
        
        if channel_idx not in channel_neighbors:
            return None
        
        neighbors = channel_neighbors[channel_idx]
        
        # 收集当前通道和邻近通道的数据
        all_channels_data = []
        
        # 当前通道
        current_data = self.eeg_data[channel_idx, window[0]:window[1]]
        hankel_current = self._create_hankel_matrix(current_data, embedding_dim)
        all_channels_data.append(hankel_current)
        
        # 邻近通道
        for neighbor_idx in neighbors:
            if neighbor_idx < len(self.channel_names):
                neighbor_data = self.eeg_data[neighbor_idx, window[0]:window[1]]
                hankel_neighbor = self._create_hankel_matrix(neighbor_data, embedding_dim)
                all_channels_data.append(hankel_neighbor)
        
        # 合并所有通道数据
        if len(all_channels_data) > 1:
            combined_matrix = np.vstack(all_channels_data)
            return combined_matrix
        else:
            return all_channels_data[0]
    
    def _estimate_source_number(self, eigenvals):
        """自动估计源数"""
        # 方法1: 特征值比例法
        if len(eigenvals) < 3:
            return 1
        
        ratios = eigenvals[:-1] / eigenvals[1:]
        max_ratio_idx = np.argmax(ratios)
        
        # 方法2: 信息论准则 (简化版AIC)
        n = len(eigenvals)
        aic_scores = []
        
        for k in range(1, min(n-1, 10)):
            signal_eigenvals = eigenvals[:k]
            noise_eigenvals = eigenvals[k:]
            
            if len(noise_eigenvals) > 0:
                log_likelihood = -n * np.sum(np.log(noise_eigenvals))
                penalty = 2 * k
                aic = -2 * log_likelihood + penalty
                aic_scores.append(aic)
        
        if aic_scores:
            aic_optimal = np.argmin(aic_scores) + 1
        else:
            aic_optimal = 1
        
        # 综合两种方法
        estimated_sources = min(max_ratio_idx + 1, aic_optimal)
        return max(1, min(estimated_sources, len(eigenvals) // 2))
    
    def _compute_music_spectrum(self, channel_idx, noise_subspace, embedding_dim):
        """计算MUSIC伪谱"""
        n_sources_spatial = len(self.source_positions)
        music_spectrum = np.zeros(n_sources_spatial)
        
        # 获取该通道的导联场
        channel_leadfield = self.leadfield[channel_idx:channel_idx+1, :]
        
        for i in range(n_sources_spatial):
            if i % 1000 == 0:
                print(f"    MUSIC计算进度: {i}/{n_sources_spatial}")
            
            # 获取源的导联场向量
            source_leadfield = channel_leadfield[:, i*3:(i+1)*3]
            
            # 选择最强方向
            max_direction = np.argmax([np.linalg.norm(source_leadfield[:, j]) for j in range(3)])
            steering_vector_base = source_leadfield[:, max_direction]
            
            if np.linalg.norm(steering_vector_base) > 0:
                # 扩展到嵌入维度
                steering_vector = np.tile(steering_vector_base, embedding_dim)
                
                # 标准化
                steering_vector = steering_vector / np.linalg.norm(steering_vector)
                
                # 确保维度匹配
                if len(steering_vector) != noise_subspace.shape[0]:
                    min_dim = min(len(steering_vector), noise_subspace.shape[0])
                    steering_vector = steering_vector[:min_dim]
                    noise_subspace_truncated = noise_subspace[:min_dim, :]
                else:
                    noise_subspace_truncated = noise_subspace
                
                # MUSIC伪谱计算
                if noise_subspace_truncated.shape[1] > 0:
                    projection = noise_subspace_truncated @ noise_subspace_truncated.T @ steering_vector
                    denominator = np.real(steering_vector.T @ projection)
                    
                    if abs(denominator) > 1e-12:
                        music_spectrum[i] = 1.0 / abs(denominator)
                    else:
                        music_spectrum[i] = 1e12
                else:
                    music_spectrum[i] = 0
            else:
                music_spectrum[i] = 0
        
        return music_spectrum
    
    def _postprocess_spectrum(self, spectrum):
        """后处理MUSIC谱"""
        # 去除异常值
        spectrum_clipped = np.clip(spectrum, 0, np.percentile(spectrum, 99.5))
        
        # 对数变换
        spectrum_log = np.log10(spectrum_clipped + 1e-12)
        
        # 标准化到[0,1]
        if np.max(spectrum_log) > np.min(spectrum_log):
            spectrum_norm = (spectrum_log - np.min(spectrum_log)) / \
                          (np.max(spectrum_log) - np.min(spectrum_log))
        else:
            spectrum_norm = np.zeros_like(spectrum_log)
        
        return spectrum_norm
    
    def _detect_peaks(self, spectrum):
        """检测MUSIC谱峰值"""
        # 自适应阈值
        threshold = max(0.6, np.mean(spectrum) + 2 * np.std(spectrum))
        
        peaks, properties = find_peaks(spectrum, 
                                     height=threshold,
                                     distance=50,  # 最小距离
                                     prominence=0.1)
        
        # 按强度排序，取前10个
        if len(peaks) > 0:
            peak_heights = spectrum[peaks]
            sorted_indices = np.argsort(peak_heights)[::-1]
            top_peaks = peaks[sorted_indices[:10]]
            return top_peaks
        else:
            return np.array([], dtype=int)

    def analyze_all_channels(self):
        """分析所有通道"""
        print("\n🎼 开始单通道MUSIC逐通道分析")
        print("="*60)

        if not self.load_data():
            print("数据加载失败")
            return False

        # 分析每个通道
        for i, channel_name in enumerate(self.channel_names):
            print(f"\n进度: {i+1}/{len(self.channel_names)}")

            try:
                result = self.single_channel_music(i)
                self.channel_results[channel_name] = result

                # 计算通道统计
                self.channel_statistics[channel_name] = self._compute_channel_statistics(result)

            except Exception as e:
                print(f"  ❌ 通道 {channel_name} 分析失败: {e}")
                continue

        print(f"\n✅ 完成 {len(self.channel_results)} 个通道的分析!")
        return True

    def _compute_channel_statistics(self, result):
        """计算通道统计信息"""
        stats = {
            'channel_name': result['channel_name'],
            'anatomy': result['anatomy'],
            'n_sources': result['n_sources'],
            'n_peaks': len(result['peaks']),
            'max_music_strength': np.max(result['music_spectrum']) if len(result['music_spectrum']) > 0 else 0,
            'mean_music_strength': np.mean(result['music_spectrum']) if len(result['music_spectrum']) > 0 else 0,
            'snr_estimate': result['snr_estimate'],
            'signal_quality': result['signal_stats']['quality'],
            'dominant_freq': 0,
            'peak_locations': []
        }

        # 主导源位置
        if len(result['peaks']) > 0:
            stats['peak_locations'] = [
                {
                    'position': pos.tolist(),
                    'strength': strength,
                    'anatomy': self._get_anatomical_region(pos)
                }
                for pos, strength in zip(result['peak_positions'], result['peak_strengths'])
            ]

        return stats

    def _get_anatomical_region(self, position):
        """根据位置推断解剖区域"""
        x, y, z = position

        if y > 0.06:  # 前部
            if abs(x) < 0.015:
                return "正中前额叶"
            elif x < -0.015:
                return "左前额叶"
            else:
                return "右前额叶"
        elif y < -0.06:  # 后部
            if z > 0.05:  # 高位
                if abs(x) < 0.015:
                    return "正中顶叶"
                elif x < -0.015:
                    return "左顶叶"
                else:
                    return "右顶叶"
            else:  # 低位
                if abs(x) < 0.015:
                    return "正中枕叶"
                elif x < -0.015:
                    return "左枕叶"
                else:
                    return "右枕叶"
        else:  # 中部
            if abs(x) < 0.015:
                return "正中中央区"
            elif x < -0.015:
                return "左中央区"
            else:
                return "右中央区"

    def print_detailed_results(self):
        """打印详细分析结果"""
        print("\n" + "="*80)
        print("🎼 单通道MUSIC详细分析结果")
        print("="*80)

        # 按最大MUSIC强度排序
        sorted_channels = sorted(self.channel_statistics.items(),
                               key=lambda x: x[1]['max_music_strength'],
                               reverse=True)

        for i, (channel_name, stats) in enumerate(sorted_channels):
            result = self.channel_results[channel_name]

            print(f"\n🎯 排名 #{i+1}: {channel_name} ({stats['anatomy']})")
            print(f"   {'='*50}")

            # 基本信息
            print(f"   📊 最大MUSIC强度: {stats['max_music_strength']:.4f}")
            print(f"   📈 平均MUSIC强度: {stats['mean_music_strength']:.4f}")
            print(f"   🎯 检测源数: {stats['n_sources']}")
            print(f"   ⭐ 峰值源数: {stats['n_peaks']}")
            print(f"   📡 信噪比: {stats['snr_estimate']:.2f}")
            print(f"   🏥 信号质量: {stats['signal_quality']}")

            # 信号特性
            signal_stats = result['signal_stats']
            print(f"   📈 信号特性:")
            print(f"      RMS: {signal_stats['rms']:.2f}")
            print(f"      Alpha功率: {signal_stats['alpha_power']:.4f}")
            print(f"      Beta功率: {signal_stats['beta_power']:.4f}")
            print(f"      SNR估计: {signal_stats['snr_estimate']:.2f}")

            # 峰值源详情
            if stats['peak_locations']:
                print(f"   🎯 检测到的源:")
                for j, peak in enumerate(stats['peak_locations'][:5]):  # 显示前5个
                    pos = peak['position']
                    print(f"      源{j+1}: 强度{peak['strength']:.3f}, "
                          f"位置({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}), "
                          f"区域: {peak['anatomy']}")
            else:
                print(f"   🎯 未检测到显著源")

        # 总体统计
        print(f"\n" + "="*80)
        print("📊 总体统计")
        print("="*80)

        all_max_strengths = [stats['max_music_strength'] for stats in self.channel_statistics.values()]
        all_mean_strengths = [stats['mean_music_strength'] for stats in self.channel_statistics.values()]
        all_n_sources = [stats['n_sources'] for stats in self.channel_statistics.values()]
        all_n_peaks = [stats['n_peaks'] for stats in self.channel_statistics.values()]

        print(f"   平均最大MUSIC强度: {np.mean(all_max_strengths):.4f} ± {np.std(all_max_strengths):.4f}")
        print(f"   平均检测源数: {np.mean(all_n_sources):.1f}")
        print(f"   平均峰值源数: {np.mean(all_n_peaks):.1f}")
        print(f"   总检测峰值数: {sum(all_n_peaks)}")

        # 信号质量统计
        quality_counts = {}
        for stats in self.channel_statistics.values():
            quality = stats['signal_quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1

        print(f"   信号质量分布:")
        for quality, count in quality_counts.items():
            percentage = count / len(self.channel_statistics) * 100
            print(f"      {quality}: {count}通道 ({percentage:.1f}%)")

    def visualize_channel_results(self, save_dir='single_channel_music_results'):
        """可视化单通道MUSIC结果"""
        import os
        os.makedirs(save_dir, exist_ok=True)

        if not self.channel_results:
            print("没有结果可显示")
            return

        # 创建大图显示所有通道
        n_channels = len(self.channel_results)
        cols = 5
        rows = (n_channels + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(25, 5*rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        axes = axes.flatten()

        for i, (channel_name, result) in enumerate(self.channel_results.items()):
            if i >= len(axes):
                break

            ax = axes[i]

            spectrum = result['music_spectrum']
            positions = self.source_positions

            # 散点图
            scatter = ax.scatter(positions[:, 0], positions[:, 1],
                               c=spectrum, cmap='hot', s=2, alpha=0.7)

            # 标记峰值源
            if len(result['peaks']) > 0:
                peak_pos = result['peak_positions']
                ax.scatter(peak_pos[:, 0], peak_pos[:, 1],
                          c='blue', s=50, marker='*', edgecolor='white', linewidth=1)

                # 添加标签
                for j, pos in enumerate(peak_pos[:3]):  # 只标记前3个
                    ax.annotate(f'{j+1}', (pos[0], pos[1]),
                              xytext=(3, 3), textcoords='offset points',
                              fontsize=8, color='blue', fontweight='bold')

            ax.set_title(f"{channel_name}\n({result['anatomy']})\n"
                        f"峰值: {len(result['peaks'])}, "
                        f"最大: {np.max(spectrum):.3f}",
                        fontsize=10)
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')

        # 隐藏多余的子图
        for i in range(len(self.channel_results), len(axes)):
            axes[i].set_visible(False)

        plt.suptitle('单通道MUSIC源定位结果 - 各通道XY平面投影', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{save_dir}/all_channels_music.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 可视化结果已保存: {save_dir}/all_channels_music.png")

    def create_summary_analysis(self, save_dir='single_channel_music_results'):
        """创建总结分析"""
        import os
        os.makedirs(save_dir, exist_ok=True)

        # 创建总结图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 收集数据
        channels = list(self.channel_statistics.keys())
        max_strengths = [self.channel_statistics[ch]['max_music_strength'] for ch in channels]
        mean_strengths = [self.channel_statistics[ch]['mean_music_strength'] for ch in channels]
        n_peaks = [self.channel_statistics[ch]['n_peaks'] for ch in channels]
        snr_estimates = [self.channel_statistics[ch]['snr_estimate'] for ch in channels]

        # 1. 最大MUSIC强度
        ax = axes[0, 0]
        bars = ax.bar(channels, max_strengths, color='skyblue')
        ax.set_title('各通道最大MUSIC强度', fontweight='bold')
        ax.set_ylabel('MUSIC强度')
        ax.tick_params(axis='x', rotation=45)

        # 标记最高的3个
        top3_indices = np.argsort(max_strengths)[-3:]
        for idx in top3_indices:
            bars[idx].set_color('red')

        # 2. 检测峰值数
        ax = axes[0, 1]
        ax.bar(channels, n_peaks, color='lightgreen')
        ax.set_title('各通道检测峰值源数', fontweight='bold')
        ax.set_ylabel('峰值源数')
        ax.tick_params(axis='x', rotation=45)

        # 3. 信噪比分布
        ax = axes[0, 2]
        ax.bar(channels, snr_estimates, color='orange')
        ax.set_title('各通道信噪比估计', fontweight='bold')
        ax.set_ylabel('信噪比')
        ax.tick_params(axis='x', rotation=45)

        # 4. MUSIC强度分布直方图
        ax = axes[1, 0]
        ax.hist(max_strengths, bins=15, alpha=0.7, color='purple', edgecolor='black')
        ax.axvline(np.mean(max_strengths), color='red', linestyle='--',
                  label=f'均值: {np.mean(max_strengths):.3f}')
        ax.set_title('MUSIC强度分布', fontweight='bold')
        ax.set_xlabel('最大MUSIC强度')
        ax.set_ylabel('通道数')
        ax.legend()

        # 5. 信号质量分布
        ax = axes[1, 1]
        quality_counts = {}
        for stats in self.channel_statistics.values():
            quality = stats['signal_quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1

        colors = {'Good': 'green', 'Fair': 'orange', 'Poor': 'red'}
        qualities = list(quality_counts.keys())
        counts = list(quality_counts.values())
        bar_colors = [colors.get(q, 'gray') for q in qualities]

        ax.bar(qualities, counts, color=bar_colors)
        ax.set_title('信号质量分布', fontweight='bold')
        ax.set_ylabel('通道数')

        # 6. 强度vs峰值数散点图
        ax = axes[1, 2]
        scatter = ax.scatter(max_strengths, n_peaks, c=snr_estimates,
                           cmap='viridis', s=100, alpha=0.7)

        # 添加通道标签
        for i, ch in enumerate(channels):
            if max_strengths[i] > np.percentile(max_strengths, 80) or n_peaks[i] > 2:
                ax.annotate(ch, (max_strengths[i], n_peaks[i]),
                          xytext=(5, 5), textcoords='offset points', fontsize=8)

        ax.set_title('MUSIC强度 vs 峰值源数', fontweight='bold')
        ax.set_xlabel('最大MUSIC强度')
        ax.set_ylabel('峰值源数')
        plt.colorbar(scatter, ax=ax, label='信噪比')

        plt.tight_layout()
        plt.savefig(f'{save_dir}/summary_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 总结分析已保存: {save_dir}/summary_analysis.png")


def main():
    """主函数"""
    import os

    print("🎼 单通道MUSIC算法EEG源定位分析")
    print("="*60)

    # 检查必要文件
    required_files = ['signal-1.csv.gz', 'hdbet_bem_output']
    missing_files = []

    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return

    try:
        # 创建分析器
        analyzer = SingleChannelMUSICAnalyzer()

        # 运行分析
        if analyzer.analyze_all_channels():
            # 打印详细结果
            analyzer.print_detailed_results()

            # 可视化结果
            analyzer.visualize_channel_results()

            # 创建总结分析
            analyzer.create_summary_analysis()

            print(f"\n🎉 单通道MUSIC分析完成!")
        else:
            print("❌ 分析失败")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
