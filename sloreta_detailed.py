#!/usr/bin/env python3
"""
详细的sLORETA (标准化低分辨率脑电磁断层成像) 实现
支持中文显示的完整源定位系统
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from matplotlib import font_manager
import pandas as pd
import nibabel as nib
from scipy import linalg
from scipy.spatial.distance import cdist
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
def setup_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试不同的中文字体
        chinese_fonts = [
            'SimHei',           # 黑体 (Windows)
            'Microsoft YaHei',  # 微软雅黑 (Windows)
            'PingFang SC',      # 苹方 (macOS)
            'Hiragino Sans GB', # 冬青黑体 (macOS)
            'WenQuanYi Micro Hei', # 文泉驿微米黑 (Linux)
            'DejaVu Sans',      # 备选字体
        ]
        
        # 获取系统可用字体
        available_fonts = [f.name for f in font_manager.fontManager.ttflist]
        
        # 选择第一个可用的中文字体
        selected_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                selected_font = font
                break
        
        if selected_font:
            plt.rcParams['font.sans-serif'] = [selected_font]
            print(f"✅ 使用中文字体: {selected_font}")
        else:
            # 如果没有找到中文字体，使用Unicode字体
            plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'DejaVu Sans']
            print("⚠️ 未找到中文字体，使用Unicode字体")
        
        # 解决负号显示问题
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置字体大小
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 14
        plt.rcParams['axes.labelsize'] = 12
        plt.rcParams['xtick.labelsize'] = 10
        plt.rcParams['ytick.labelsize'] = 10
        plt.rcParams['legend.fontsize'] = 10
        
    except Exception as e:
        print(f"字体设置失败: {e}")
        print("将使用默认字体")

class DetailedSLORETA:
    """详细的sLORETA实现"""
    
    def __init__(self, bem_output_dir='hdbet_bem_output', eeg_file='signal-1.csv.gz'):
        """
        初始化详细的sLORETA系统
        
        Parameters:
        -----------
        bem_output_dir : str
            BEM模型输出目录
        eeg_file : str
            EEG信号文件路径
        """
        # 设置中文字体
        setup_chinese_font()
        
        self.bem_output_dir = bem_output_dir
        self.eeg_file = eeg_file
        
        # 数据存储
        self.eeg_data = None
        self.eeg_info = None
        self.leadfield = None
        self.source_positions = None
        self.noise_cov = None
        
        # sLORETA特定参数
        self.lambda_reg = 0.05  # 正则化参数
        self.standardization_matrix = None
        self.inverse_operator = None
        self.source_estimates = None
        
        # 标准10-20电极位置和中文名称
        self.electrode_info = {
            'Fp1': {'pos': [-0.0308, 0.0954, 0.0062], 'name_cn': '左前极'},
            'Fp2': {'pos': [0.0308, 0.0954, 0.0062], 'name_cn': '右前极'},
            'F7': {'pos': [-0.0761, 0.0578, 0.0057], 'name_cn': '左前颞'},
            'F3': {'pos': [-0.0545, 0.0676, 0.0645], 'name_cn': '左前额'},
            'Fz': {'pos': [0.0000, 0.0676, 0.0676], 'name_cn': '前额中线'},
            'F4': {'pos': [0.0545, 0.0676, 0.0645], 'name_cn': '右前额'},
            'F8': {'pos': [0.0761, 0.0578, 0.0057], 'name_cn': '右前颞'},
            'T7': {'pos': [-0.0900, 0.0000, 0.0000], 'name_cn': '左颞'},
            'C3': {'pos': [-0.0636, 0.0000, 0.0636], 'name_cn': '左中央'},
            'Cz': {'pos': [0.0000, 0.0000, 0.0900], 'name_cn': '中央中线'},
            'C4': {'pos': [0.0636, 0.0000, 0.0636], 'name_cn': '右中央'},
            'T8': {'pos': [0.0900, 0.0000, 0.0000], 'name_cn': '右颞'},
            'P7': {'pos': [-0.0761, -0.0578, 0.0057], 'name_cn': '左后颞'},
            'P3': {'pos': [-0.0545, -0.0676, 0.0645], 'name_cn': '左顶'},
            'Pz': {'pos': [0.0000, -0.0676, 0.0676], 'name_cn': '顶中线'},
            'P4': {'pos': [0.0545, -0.0676, 0.0645], 'name_cn': '右顶'},
            'P8': {'pos': [0.0761, -0.0578, 0.0057], 'name_cn': '右后颞'},
            'O1': {'pos': [-0.0308, -0.0954, 0.0062], 'name_cn': '左枕'},
            'O2': {'pos': [0.0308, -0.0954, 0.0062], 'name_cn': '右枕'}
        }
        
        print("详细sLORETA系统初始化完成")
        print(f"BEM模型目录: {bem_output_dir}")
        print(f"EEG信号文件: {eeg_file}")
    
    def load_data(self):
        """加载EEG数据和BEM模型"""
        print("\n" + "="*60)
        print("📊 数据加载阶段")
        print("="*60)
        
        # 加载EEG数据
        success = self._load_eeg_data()
        if not success:
            return False
        
        # 加载BEM模型和创建源空间
        success = self._load_bem_and_sources()
        if not success:
            return False
        
        # 计算导联场矩阵
        success = self._compute_leadfield()
        if not success:
            return False
        
        print("✅ 所有数据加载完成")
        return True
    
    def _load_eeg_data(self):
        """加载EEG数据"""
        try:
            print("正在加载EEG数据...")
            
            # 读取CSV数据
            df = pd.read_csv(self.eeg_file, compression='gzip')
            
            # 提取EEG信号 (前19列)
            n_channels = 19
            eeg_signals = df.iloc[:, :n_channels].values.T
            
            # 创建通道信息
            ch_names = list(self.electrode_info.keys())[:n_channels]
            sfreq = 250.0
            
            # 存储数据
            self.eeg_data = eeg_signals
            self.eeg_info = {
                'ch_names': ch_names,
                'sfreq': sfreq,
                'n_channels': n_channels,
                'n_times': eeg_signals.shape[1]
            }
            
            print(f"  ✅ EEG数据: {n_channels}通道 × {eeg_signals.shape[1]}时间点")
            print(f"  ✅ 采样率: {sfreq} Hz")
            print(f"  ✅ 时长: {eeg_signals.shape[1]/sfreq:.1f} 秒")
            
            return True
            
        except Exception as e:
            print(f"❌ EEG数据加载失败: {e}")
            return False
    
    def _load_bem_and_sources(self):
        """加载BEM模型并创建源空间"""
        try:
            print("正在加载BEM模型和创建源空间...")
            
            # 从分割结果创建源空间
            seg_file = f"{self.bem_output_dir}/segmentation.nii.gz"
            seg_img = nib.load(seg_file)
            seg_data = seg_img.get_fdata()
            
            # 获取脑组织区域 (灰质=4, 白质=5)
            brain_mask = (seg_data == 4) | (seg_data == 5)
            
            # 获取脑组织的体素坐标
            brain_coords = np.array(np.where(brain_mask)).T
            
            # 转换到物理坐标
            affine = seg_img.affine
            brain_coords_phys = nib.affines.apply_affine(affine, brain_coords)
            
            # 采样源点
            n_sources = min(3000, len(brain_coords_phys))  # 减少源点数量以提高计算效率
            if len(brain_coords_phys) > n_sources:
                indices = np.random.choice(len(brain_coords_phys), n_sources, replace=False)
                self.source_positions = brain_coords_phys[indices] / 1000.0  # 转换为米
            else:
                self.source_positions = brain_coords_phys / 1000.0
            
            print(f"  ✅ 源空间: {len(self.source_positions)}个源点")
            print(f"  ✅ 坐标范围: X[{np.min(self.source_positions[:,0]):.3f}, {np.max(self.source_positions[:,0]):.3f}]")
            print(f"              Y[{np.min(self.source_positions[:,1]):.3f}, {np.max(self.source_positions[:,1]):.3f}]")
            print(f"              Z[{np.min(self.source_positions[:,2]):.3f}, {np.max(self.source_positions[:,2]):.3f}]")
            
            return True
            
        except Exception as e:
            print(f"❌ BEM模型加载失败: {e}")
            return False
