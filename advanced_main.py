#!/usr/bin/env python3
"""
高级5层BEM模型构建主程序
使用开源深度学习分割模型获得更高精度
"""

import os
import sys
import argparse
from advanced_bem_builder import AdvancedBEMBuilder

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='使用开源模型构建高精度5层BEM模型')
    parser.add_argument('mri_file', help='MRI文件路径 (.nii或.nii.gz)')
    parser.add_argument('--method', '-m', 
                       choices=['synthseg', 'fastsurfer', 'hd-bet', 'traditional', 'auto'],
                       default='auto',
                       help='分割方法 (默认: auto)')
    parser.add_argument('--output', '-o', default='advanced_bem_output', 
                       help='输出目录 (默认: advanced_bem_output)')
    parser.add_argument('--visualize', '-v', action='store_true',
                       help='显示3D可视化')
    parser.add_argument('--save-screenshot', '-s', 
                       help='保存可视化截图路径')
    parser.add_argument('--check-only', '-c', action='store_true',
                       help='仅检查可用的分割模型')
    
    args = parser.parse_args()
    
    print("="*70)
    print("高级5层BEM模型构建器 (集成开源深度学习模型)")
    print("="*70)
    
    try:
        # 初始化构建器
        builder = AdvancedBEMBuilder(args.mri_file, args.method)
        
        # 检查依赖
        available_methods = builder.check_dependencies()
        
        if args.check_only:
            print("\n可用的分割方法:")
            for method in available_methods:
                print(f"  ✅ {method}")
            
            if not available_methods or available_methods == ['traditional']:
                print("\n建议安装开源分割模型以获得更高精度:")
                print("  python install_segmentation_models.py all")
            
            return
        
        # 检查输入文件
        if not os.path.exists(args.mri_file):
            print(f"错误: MRI文件不存在: {args.mri_file}")
            sys.exit(1)
        
        print(f"\n使用分割方法: {builder.segmentation_method}")
        
        # 步骤1: 加载MRI数据
        print("\n步骤1: 加载MRI数据")
        if not builder.load_mri_data():
            print("加载MRI数据失败")
            sys.exit(1)
        
        # 步骤2: 高精度组织分割
        print(f"\n步骤2: 使用{builder.segmentation_method}进行高精度组织分割")
        if not builder.segment_tissues():
            print("组织分割失败")
            sys.exit(1)
        
        # 步骤3: 提取表面
        print("\n步骤3: 提取表面")
        builder.extract_surfaces()
        
        # 步骤4: 构建BEM模型
        print("\n步骤4: 构建BEM模型")
        if not builder.build_bem_model():
            print("构建BEM模型失败")
            sys.exit(1)
        
        # 步骤5: 保存结果
        print("\n步骤5: 保存结果")
        builder.save_model(args.output)
        
        # 显示模型信息
        print("\n模型信息:")
        info = builder.get_model_info()
        print(f"分割方法: {info['segmentation_method']}")
        print(f"MRI形状: {info['mri_shape']}")
        print(f"体素尺寸: {info['voxel_size']}")
        print(f"表面数量: {len(info['surfaces'])}")
        
        for surface_name, surface_info in info['surfaces'].items():
            print(f"  {surface_name}: {surface_info['vertices_count']:,}个顶点, "
                  f"{surface_info['faces_count']:,}个面")
        
        print(f"BEM模型就绪: {info['bem_model_ready']}")
        print(f"BEM解就绪: {info['bem_solution_ready']}")
        
        # 可视化
        if args.visualize:
            print("\n步骤6: 可视化")
            builder.visualize_model(args.save_screenshot)
        
        print("\n" + "="*70)
        print("高精度BEM模型构建完成!")
        print(f"分割方法: {builder.segmentation_method}")
        print(f"输出目录: {args.output}")
        print("="*70)
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def run_comparison():
    """运行不同方法的比较"""
    print("运行分割方法比较...")
    
    mri_file = "sub-1_T1w.nii"
    
    if not os.path.exists(mri_file):
        print(f"示例MRI文件不存在: {mri_file}")
        return
    
    methods = ['traditional', 'synthseg', 'fastsurfer', 'hd-bet']
    results = {}
    
    for method in methods:
        print(f"\n{'='*50}")
        print(f"测试方法: {method}")
        print(f"{'='*50}")
        
        try:
            builder = AdvancedBEMBuilder(mri_file, method)
            available = builder.check_dependencies()
            
            if method not in available and method != 'traditional':
                print(f"⚠️  {method} 不可用，跳过")
                continue
            
            # 运行完整流程
            builder.load_mri_data()
            builder.segment_tissues()
            
            # 收集统计信息
            tissue_counts = {}
            for label in range(6):
                count = np.sum(builder.segmentation == label)
                tissue_counts[label] = count
            
            results[method] = {
                'tissue_counts': tissue_counts,
                'brain_ratio': (tissue_counts[4] + tissue_counts[5]) / builder.segmentation.size * 100
            }
            
            print(f"✅ {method} 完成")
            
        except Exception as e:
            print(f"❌ {method} 失败: {e}")
    
    # 显示比较结果
    print("\n" + "="*70)
    print("分割方法比较结果")
    print("="*70)
    
    tissue_names = ['背景', '头皮', '颅骨', '脑脊液', '灰质', '白质']
    
    for method, result in results.items():
        print(f"\n{method.upper()}:")
        for label, name in enumerate(tissue_names):
            count = result['tissue_counts'][label]
            print(f"  {name}: {count:,} 体素")
        print(f"  脑组织比例: {result['brain_ratio']:.1f}%")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有参数时显示帮助
        print("高级5层BEM模型构建器")
        print("\n使用方法:")
        print("  python advanced_main.py <mri_file> [选项]")
        print("\n示例:")
        print("  python advanced_main.py sub-1_T1w.nii --method auto")
        print("  python advanced_main.py sub-1_T1w.nii --method synthseg --visualize")
        print("  python advanced_main.py sub-1_T1w.nii --check-only")
        print("\n运行 'python advanced_main.py --help' 查看所有选项")
        
        # 提供快速测试选项
        choice = input("\n是否运行分割方法比较测试? (y/n): ").strip().lower()
        if choice == 'y':
            import numpy as np
            run_comparison()
    else:
        main()
