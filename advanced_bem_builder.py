"""
高级BEM模型构建器 - 集成开源精准分割模型
支持SynthSeg、FastSurfer、HD-BET等深度学习模型
"""

import os
import sys
import numpy as np
import nibabel as nib
from scipy import ndimage
from skimage import measure, morphology
import subprocess
import tempfile
import warnings
warnings.filterwarnings('ignore')

class AdvancedBEMBuilder:
    """高级BEM模型构建器 - 使用开源深度学习分割模型"""
    
    def __init__(self, mri_file_path, segmentation_method='synthseg'):
        """
        初始化高级BEM构建器
        
        Parameters:
        -----------
        mri_file_path : str
            MRI文件路径
        segmentation_method : str
            分割方法: 'synthseg', 'fastsurfer', 'hd-bet', 'auto'
        """
        self.mri_file_path = mri_file_path
        self.segmentation_method = segmentation_method
        self.mri_data = None
        self.affine = None
        self.header = None
        self.segmentation = None
        self.surfaces = {}
        self.bem_model = None
        self.bem_solution = None
        
        # 5层组织的电导率值 (S/m)
        self.conductivities = {
            'scalp': 0.33,
            'skull': 0.0042,
            'csf': 1.79,
            'gray_matter': 0.33,
            'white_matter': 0.14
        }
        
        print(f"初始化高级BEM构建器")
        print(f"MRI文件: {mri_file_path}")
        print(f"分割方法: {segmentation_method}")
    
    def check_dependencies(self):
        """检查依赖和模型可用性"""
        print("检查依赖和模型可用性...")
        
        available_methods = []
        
        # 检查SynthSeg
        try:
            import tensorflow as tf
            # 检查SynthSeg是否可用
            if self._check_synthseg():
                available_methods.append('synthseg')
                print("✅ SynthSeg 可用")
            else:
                print("❌ SynthSeg 不可用")
        except ImportError:
            print("❌ SynthSeg 需要 TensorFlow")
        
        # 检查FastSurfer
        if self._check_fastsurfer():
            available_methods.append('fastsurfer')
            print("✅ FastSurfer 可用")
        else:
            print("❌ FastSurfer 不可用")
        
        # 检查HD-BET
        if self._check_hdbet():
            available_methods.append('hd-bet')
            print("✅ HD-BET 可用")
        else:
            print("❌ HD-BET 不可用")
        
        if not available_methods:
            print("⚠️  没有找到可用的深度学习分割模型")
            print("将使用改进的传统分割算法")
            available_methods.append('traditional')
        
        # 自动选择最佳方法
        if self.segmentation_method == 'auto':
            if 'synthseg' in available_methods:
                self.segmentation_method = 'synthseg'
            elif 'fastsurfer' in available_methods:
                self.segmentation_method = 'fastsurfer'
            elif 'hd-bet' in available_methods:
                self.segmentation_method = 'hd-bet'
            else:
                self.segmentation_method = 'traditional'
        
        print(f"选择的分割方法: {self.segmentation_method}")
        return available_methods
    
    def _check_synthseg(self):
        """检查SynthSeg是否可用"""
        try:
            # 尝试导入SynthSeg
            sys.path.append('/path/to/SynthSeg')  # 需要根据实际安装路径调整
            import SynthSeg
            return True
        except:
            # 检查是否可以通过命令行调用
            try:
                result = subprocess.run(['python', '-c', 'import SynthSeg'], 
                                      capture_output=True, text=True)
                return result.returncode == 0
            except:
                return False
    
    def _check_fastsurfer(self):
        """检查FastSurfer是否可用"""
        try:
            result = subprocess.run(['run_fastsurfer.py', '--help'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def _check_hdbet(self):
        """检查HD-BET是否可用"""
        try:
            result = subprocess.run(['hd-bet', '--help'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def load_mri_data(self):
        """加载MRI数据"""
        try:
            print("正在加载MRI数据...")
            nii_img = nib.load(self.mri_file_path)
            self.mri_data = nii_img.get_fdata()
            self.affine = nii_img.affine
            self.header = nii_img.header
            
            print(f"MRI数据形状: {self.mri_data.shape}")
            print(f"体素尺寸: {self.header.get_zooms()}")
            print("MRI数据加载完成")
            return True
            
        except Exception as e:
            print(f"加载MRI数据失败: {e}")
            return False
    
    def segment_with_synthseg(self):
        """使用SynthSeg进行分割"""
        print("使用SynthSeg进行脑分割...")
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as temp_input:
                temp_input_path = temp_input.name
            
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as temp_output:
                temp_output_path = temp_output.name
            
            # 保存输入文件
            nib.save(nib.Nifti1Image(self.mri_data, self.affine, self.header), temp_input_path)
            
            # 运行SynthSeg
            cmd = [
                'python', '-m', 'SynthSeg.scripts.commands.SynthSeg_predict',
                '--i', temp_input_path,
                '--o', temp_output_path,
                '--robust', '1',
                '--fast', '0'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 加载分割结果
                seg_img = nib.load(temp_output_path)
                synthseg_labels = seg_img.get_fdata()
                
                # 转换SynthSeg标签到我们的5层标签
                self.segmentation = self._convert_synthseg_labels(synthseg_labels)
                
                print("SynthSeg分割完成")
                return True
            else:
                print(f"SynthSeg运行失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"SynthSeg分割失败: {e}")
            return False
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_input_path)
                os.unlink(temp_output_path)
            except:
                pass
    
    def _convert_synthseg_labels(self, synthseg_labels):
        """将SynthSeg标签转换为5层BEM标签"""
        # SynthSeg标签映射 (基于FreeSurfer标签)
        # 这是一个简化的映射，实际使用时需要根据SynthSeg的具体标签调整
        
        bem_segmentation = np.zeros_like(synthseg_labels, dtype=np.uint8)
        
        # 背景
        bem_segmentation[synthseg_labels == 0] = 0
        
        # 头皮 (可能需要从其他组织推断)
        scalp_labels = [3, 4, 5, 6, 7, 8, 12, 13, 14, 15, 16, 17, 18]  # 皮质和皮下组织
        scalp_mask = np.isin(synthseg_labels, scalp_labels)
        scalp_mask = self._get_outer_surface_mask(scalp_mask)
        bem_segmentation[scalp_mask] = 1
        
        # 颅骨 (通常需要从其他信息推断)
        # 这里使用一个简化的方法
        brain_mask = synthseg_labels > 0
        brain_dilated = morphology.binary_dilation(brain_mask, morphology.ball(3))
        skull_mask = brain_dilated & ~brain_mask
        bem_segmentation[skull_mask] = 2
        
        # 脑脊液
        csf_labels = [4, 5, 14, 15, 24, 31, 63]  # 脑室和脑脊液
        bem_segmentation[np.isin(synthseg_labels, csf_labels)] = 3
        
        # 灰质
        gray_labels = [3, 8, 42, 47]  # 皮质灰质
        bem_segmentation[np.isin(synthseg_labels, gray_labels)] = 4
        
        # 白质
        white_labels = [2, 7, 16, 28, 41, 46, 60]  # 白质
        bem_segmentation[np.isin(synthseg_labels, white_labels)] = 5
        
        return bem_segmentation
    
    def _get_outer_surface_mask(self, mask):
        """获取外表面掩码"""
        eroded = morphology.binary_erosion(mask, morphology.ball(2))
        return mask & ~eroded

    def segment_with_fastsurfer(self):
        """使用FastSurfer进行分割"""
        print("使用FastSurfer进行脑分割...")

        try:
            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                input_path = os.path.join(temp_dir, 'input.nii.gz')
                output_dir = os.path.join(temp_dir, 'output')

                # 保存输入文件
                nib.save(nib.Nifti1Image(self.mri_data, self.affine, self.header), input_path)

                # 运行FastSurfer (仅分割部分)
                cmd = [
                    'run_fastsurfer.py',
                    '--t1', input_path,
                    '--sid', 'subject',
                    '--sd', output_dir,
                    '--seg_only',  # 只运行分割，不做表面重建
                    '--py', 'python'
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    # 加载分割结果
                    seg_file = os.path.join(output_dir, 'subject', 'mri', 'aparc+aseg.mgz')
                    if os.path.exists(seg_file):
                        seg_img = nib.load(seg_file)
                        fastsurfer_labels = seg_img.get_fdata()

                        # 转换FastSurfer标签到我们的5层标签
                        self.segmentation = self._convert_fastsurfer_labels(fastsurfer_labels)

                        print("FastSurfer分割完成")
                        return True
                    else:
                        print("FastSurfer输出文件未找到")
                        return False
                else:
                    print(f"FastSurfer运行失败: {result.stderr}")
                    return False

        except Exception as e:
            print(f"FastSurfer分割失败: {e}")
            return False

    def _convert_fastsurfer_labels(self, fastsurfer_labels):
        """将FastSurfer标签转换为5层BEM标签"""
        # FastSurfer使用FreeSurfer标签系统
        bem_segmentation = np.zeros_like(fastsurfer_labels, dtype=np.uint8)

        # 背景
        bem_segmentation[fastsurfer_labels == 0] = 0

        # 头皮 (从脑组织外推)
        brain_labels = [2, 3, 4, 5, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26, 28,
                       41, 42, 43, 44, 46, 47, 49, 50, 51, 52, 53, 54, 58, 60]
        brain_mask = np.isin(fastsurfer_labels, brain_labels)

        # 创建头部掩码
        head_mask = fastsurfer_labels > 0
        head_dilated = morphology.binary_dilation(head_mask, morphology.ball(5))
        scalp_mask = head_dilated & ~morphology.binary_dilation(brain_mask, morphology.ball(3))
        scalp_mask = self._get_outer_surface_mask(scalp_mask)
        bem_segmentation[scalp_mask] = 1

        # 颅骨 (脑组织和头皮之间)
        brain_dilated = morphology.binary_dilation(brain_mask, morphology.ball(2))
        skull_mask = head_mask & ~brain_dilated & ~scalp_mask
        bem_segmentation[skull_mask] = 2

        # 脑脊液
        csf_labels = [4, 5, 14, 15, 24, 31, 63]
        bem_segmentation[np.isin(fastsurfer_labels, csf_labels)] = 3

        # 灰质
        gray_labels = [3, 8, 42, 47]  # 皮质灰质
        bem_segmentation[np.isin(fastsurfer_labels, gray_labels)] = 4

        # 白质
        white_labels = [2, 7, 16, 28, 41, 46, 60]
        bem_segmentation[np.isin(fastsurfer_labels, white_labels)] = 5

        return bem_segmentation

    def segment_with_hdbet(self):
        """使用HD-BET进行脑提取，然后进行组织分割"""
        print("使用HD-BET进行脑提取...")

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as temp_input:
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as temp_output:
                temp_output_path = temp_output.name

            # 保存输入文件
            nib.save(nib.Nifti1Image(self.mri_data, self.affine, self.header), temp_input_path)

            # 运行HD-BET
            cmd = ['hd-bet', '-i', temp_input_path, '-o', temp_output_path]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 加载脑提取结果
                brain_img = nib.load(temp_output_path)
                brain_data = brain_img.get_fdata()

                # 加载脑掩码
                mask_path = temp_output_path.replace('.nii.gz', '_mask.nii.gz')
                if os.path.exists(mask_path):
                    mask_img = nib.load(mask_path)
                    brain_mask = mask_img.get_fdata() > 0

                    # 使用改进的传统方法对脑提取结果进行组织分割
                    self.segmentation = self._segment_extracted_brain(brain_data, brain_mask)

                    print("HD-BET脑提取和分割完成")
                    return True
                else:
                    print("HD-BET掩码文件未找到")
                    return False
            else:
                print(f"HD-BET运行失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"HD-BET分割失败: {e}")
            return False
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_input_path)
                os.unlink(temp_output_path)
                mask_path = temp_output_path.replace('.nii.gz', '_mask.nii.gz')
                if os.path.exists(mask_path):
                    os.unlink(mask_path)
            except:
                pass

    def _segment_extracted_brain(self, brain_data, brain_mask):
        """对HD-BET提取的脑进行组织分割"""
        bem_segmentation = np.zeros_like(brain_data, dtype=np.uint8)

        # 标准化脑数据
        brain_values = brain_data[brain_mask]
        if len(brain_values) > 0:
            p1, p99 = np.percentile(brain_values, [1, 99])
            normalized = np.clip((brain_data - p1) / (p99 - p1), 0, 1)
        else:
            normalized = brain_data

        # 在脑掩码内进行组织分割
        # 白质 (高强度)
        white_matter_mask = (normalized > 0.6) & brain_mask
        white_matter_mask = morphology.remove_small_objects(white_matter_mask, min_size=1000)
        bem_segmentation[white_matter_mask] = 5

        # 灰质 (中等强度)
        gray_matter_mask = (normalized > 0.3) & (normalized <= 0.6) & brain_mask & ~white_matter_mask
        gray_matter_mask = morphology.remove_small_objects(gray_matter_mask, min_size=500)
        bem_segmentation[gray_matter_mask] = 4

        # 脑脊液 (低强度)
        csf_mask = (normalized <= 0.3) & brain_mask & ~gray_matter_mask & ~white_matter_mask
        csf_mask = morphology.remove_small_objects(csf_mask, min_size=100)
        bem_segmentation[csf_mask] = 3

        # 头皮和颅骨 (脑外区域)
        head_mask = self.mri_data > np.percentile(self.mri_data[self.mri_data > 0], 5)

        # 颅骨
        brain_dilated = morphology.binary_dilation(brain_mask, morphology.ball(3))
        skull_region = head_mask & ~brain_dilated
        skull_mask = skull_region & (self.mri_data > np.percentile(self.mri_data[skull_region], 30))
        bem_segmentation[skull_mask] = 2

        # 头皮
        scalp_region = head_mask & ~morphology.binary_dilation(brain_dilated, morphology.ball(2))
        scalp_mask = self._get_outer_surface_mask(scalp_region)
        bem_segmentation[scalp_mask] = 1

        return bem_segmentation

    def segment_tissues(self):
        """使用选定的方法进行组织分割"""
        print(f"使用 {self.segmentation_method} 进行组织分割...")

        success = False

        if self.segmentation_method == 'synthseg':
            success = self.segment_with_synthseg()
        elif self.segmentation_method == 'fastsurfer':
            success = self.segment_with_fastsurfer()
        elif self.segmentation_method == 'hd-bet':
            success = self.segment_with_hdbet()
        elif self.segmentation_method == 'traditional':
            # 使用改进的传统方法 (从原始BEM构建器导入)
            from bem_model_builder import BEMModelBuilder
            traditional_builder = BEMModelBuilder(self.mri_file_path)
            traditional_builder.mri_data = self.mri_data
            traditional_builder.affine = self.affine
            traditional_builder.header = self.header
            traditional_builder.segment_tissues()
            self.segmentation = traditional_builder.segmentation
            success = True

        if not success:
            print(f"使用 {self.segmentation_method} 分割失败，回退到传统方法")
            # 回退到传统方法
            from bem_model_builder import BEMModelBuilder
            traditional_builder = BEMModelBuilder(self.mri_file_path)
            traditional_builder.mri_data = self.mri_data
            traditional_builder.affine = self.affine
            traditional_builder.header = self.header
            traditional_builder.segment_tissues()
            self.segmentation = traditional_builder.segmentation

        # 打印分割统计
        self._print_segmentation_stats()

        return True

    def _print_segmentation_stats(self):
        """打印分割统计信息"""
        tissue_names = {
            0: '背景',
            1: '头皮',
            2: '颅骨',
            3: '脑脊液',
            4: '灰质',
            5: '白质'
        }

        total_voxels = self.segmentation.size

        print(f"\n{self.segmentation_method.upper()} 分割统计:")
        for label, name in tissue_names.items():
            count = np.sum(self.segmentation == label)
            percentage = (count / total_voxels) * 100
            print(f"  {name}: {count:,} 体素 ({percentage:.1f}%)")

        # 检查脑组织比例
        brain_voxels = np.sum((self.segmentation == 4) | (self.segmentation == 5))
        brain_percentage = (brain_voxels / total_voxels) * 100

        if brain_percentage < 3:
            print(f"⚠️  脑组织比例较低 ({brain_percentage:.1f}%)")
        else:
            print(f"✅ 脑组织总计: {brain_voxels:,} 体素 ({brain_percentage:.1f}%)")

    # 继承其他方法 (表面提取、BEM构建等)
    def extract_surfaces(self):
        """从分割结果提取5层表面"""
        from bem_model_builder import BEMModelBuilder

        # 创建临时构建器来使用现有的表面提取方法
        temp_builder = BEMModelBuilder(self.mri_file_path)
        temp_builder.mri_data = self.mri_data
        temp_builder.affine = self.affine
        temp_builder.header = self.header
        temp_builder.segmentation = self.segmentation

        # 提取表面
        temp_builder.extract_surfaces()
        self.surfaces = temp_builder.surfaces

    def build_bem_model(self):
        """构建BEM模型"""
        from bem_model_builder import BEMModelBuilder

        # 创建临时构建器来使用现有的BEM构建方法
        temp_builder = BEMModelBuilder(self.mri_file_path)
        temp_builder.mri_data = self.mri_data
        temp_builder.affine = self.affine
        temp_builder.header = self.header
        temp_builder.segmentation = self.segmentation
        temp_builder.surfaces = self.surfaces
        temp_builder.conductivities = self.conductivities

        # 构建BEM模型
        success = temp_builder.build_bem_model()
        self.bem_model = temp_builder.bem_model
        self.bem_solution = temp_builder.bem_solution

        return success

    def visualize_model(self, save_path=None):
        """可视化BEM模型"""
        from bem_model_builder import BEMModelBuilder

        temp_builder = BEMModelBuilder(self.mri_file_path)
        temp_builder.mri_data = self.mri_data
        temp_builder.segmentation = self.segmentation
        temp_builder.surfaces = self.surfaces

        temp_builder.visualize_model(save_path)

    def save_model(self, output_dir):
        """保存BEM模型"""
        from bem_model_builder import BEMModelBuilder

        temp_builder = BEMModelBuilder(self.mri_file_path)
        temp_builder.mri_data = self.mri_data
        temp_builder.affine = self.affine
        temp_builder.header = self.header
        temp_builder.segmentation = self.segmentation
        temp_builder.surfaces = self.surfaces
        temp_builder.bem_model = self.bem_model
        temp_builder.bem_solution = self.bem_solution

        temp_builder.save_model(output_dir)

    def get_model_info(self):
        """获取模型信息"""
        info = {
            'segmentation_method': self.segmentation_method,
            'mri_shape': self.mri_data.shape if self.mri_data is not None else None,
            'voxel_size': self.header.get_zooms() if self.header else None,
            'surfaces': {},
            'conductivities': self.conductivities,
            'bem_model_ready': self.bem_model is not None,
            'bem_solution_ready': self.bem_solution is not None
        }

        for surface_name, surface_data in self.surfaces.items():
            info['surfaces'][surface_name] = {
                'vertices_count': len(surface_data['vertices']),
                'faces_count': len(surface_data['faces'])
            }

        return info
