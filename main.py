#!/usr/bin/env python3
"""
5层BEM模型构建主程序
使用示例和完整的处理流程
"""

import os
import sys
import argparse
from bem_model_builder import BEMModelBuilder

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='构建5层BEM模型')
    parser.add_argument('mri_file', help='MRI文件路径 (.nii或.nii.gz)')
    parser.add_argument('--output', '-o', default='bem_output', 
                       help='输出目录 (默认: bem_output)')
    parser.add_argument('--visualize', '-v', action='store_true',
                       help='显示3D可视化')
    parser.add_argument('--save-screenshot', '-s', 
                       help='保存可视化截图路径')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.mri_file):
        print(f"错误: MRI文件不存在: {args.mri_file}")
        sys.exit(1)
    
    print("="*60)
    print("5层BEM模型构建器")
    print("="*60)
    
    try:
        # 初始化构建器
        builder = BEMModelBuilder(args.mri_file)
        
        # 步骤1: 加载MRI数据
        print("\n步骤1: 加载MRI数据")
        if not builder.load_mri_data():
            print("加载MRI数据失败")
            sys.exit(1)
        
        # 步骤2: 预处理
        print("\n步骤2: 预处理MRI数据")
        builder.preprocess_mri()
        
        # 步骤3: 组织分割
        print("\n步骤3: 组织分割")
        builder.segment_tissues()
        
        # 步骤4: 提取表面
        print("\n步骤4: 提取表面")
        builder.extract_surfaces()
        
        # 步骤5: 构建BEM模型
        print("\n步骤5: 构建BEM模型")
        if not builder.build_bem_model():
            print("构建BEM模型失败")
            sys.exit(1)
        
        # 步骤6: 保存结果
        print("\n步骤6: 保存结果")
        builder.save_model(args.output)
        
        # 显示模型信息
        print("\n模型信息:")
        info = builder.get_model_info()
        print(f"MRI形状: {info['mri_shape']}")
        print(f"体素尺寸: {info['voxel_size']}")
        print(f"表面数量: {len(info['surfaces'])}")
        
        for surface_name, surface_info in info['surfaces'].items():
            print(f"  {surface_name}: {surface_info['vertices_count']}个顶点, "
                  f"{surface_info['faces_count']}个面")
        
        print(f"BEM模型就绪: {info['bem_model_ready']}")
        print(f"BEM解就绪: {info['bem_solution_ready']}")
        
        # 可视化
        if args.visualize:
            print("\n步骤7: 可视化")
            builder.visualize_model(args.save_screenshot)
        else:
            # 显示2D切片
            builder._plot_2d_slices()
        
        print("\n="*60)
        print("BEM模型构建完成!")
        print(f"输出目录: {args.output}")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def run_example():
    """运行示例"""
    print("运行BEM模型构建示例...")
    
    # 使用当前目录中的MRI文件
    mri_file = "sub-1_T1w.nii"
    
    if not os.path.exists(mri_file):
        print(f"示例MRI文件不存在: {mri_file}")
        print("请确保当前目录中有MRI文件")
        return
    
    try:
        # 创建构建器
        builder = BEMModelBuilder(mri_file)
        
        # 完整流程
        print("加载MRI数据...")
        builder.load_mri_data()
        
        print("预处理...")
        builder.preprocess_mri()
        
        print("组织分割...")
        builder.segment_tissues()
        
        print("提取表面...")
        builder.extract_surfaces()
        
        print("构建BEM模型...")
        builder.build_bem_model()
        
        print("保存结果...")
        builder.save_model("example_output")
        
        print("显示结果...")
        info = builder.get_model_info()
        print("模型信息:", info)
        
        # 显示2D可视化
        builder._plot_2d_slices()
        
        print("示例运行完成!")
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有参数时运行示例
        run_example()
    else:
        # 有参数时运行主程序
        main()
