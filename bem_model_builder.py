"""
5层BEM模型构建器
用于从MRI数据构建包含头皮、颅骨、脑脊液、灰质、白质的5层BEM模型
"""

import os
import numpy as np
import nibabel as nib
from scipy import ndimage
from skimage import measure, morphology
import matplotlib.pyplot as plt
import mne
from mne.bem import make_bem_model, make_bem_solution
import warnings
warnings.filterwarnings('ignore')

class BEMModelBuilder:
    """5层BEM模型构建器"""
    
    def __init__(self, mri_file_path):
        """
        初始化BEM模型构建器
        
        Parameters:
        -----------
        mri_file_path : str
            MRI文件路径（.nii或.nii.gz格式）
        """
        self.mri_file_path = mri_file_path
        self.mri_data = None
        self.affine = None
        self.header = None
        self.segmentation = None
        self.surfaces = {}
        self.bem_model = None
        self.bem_solution = None
        
        # 5层组织的电导率值 (S/m)
        self.conductivities = {
            'scalp': 0.33,      # 头皮
            'skull': 0.0042,    # 颅骨
            'csf': 1.79,        # 脑脊液
            'gray_matter': 0.33, # 灰质
            'white_matter': 0.14 # 白质
        }
        
        print(f"初始化BEM模型构建器，MRI文件: {mri_file_path}")
    
    def load_mri_data(self):
        """加载MRI数据"""
        try:
            print("正在加载MRI数据...")
            nii_img = nib.load(self.mri_file_path)
            self.mri_data = nii_img.get_fdata()
            self.affine = nii_img.affine
            self.header = nii_img.header
            
            print(f"MRI数据形状: {self.mri_data.shape}")
            print(f"体素尺寸: {self.header.get_zooms()}")
            print("MRI数据加载完成")
            return True
            
        except Exception as e:
            print(f"加载MRI数据失败: {e}")
            return False
    
    def preprocess_mri(self):
        """预处理MRI数据"""
        print("正在预处理MRI数据...")
        
        # 标准化强度值
        self.mri_data = (self.mri_data - np.min(self.mri_data)) / (np.max(self.mri_data) - np.min(self.mri_data))
        
        # 高斯滤波去噪
        self.mri_data = ndimage.gaussian_filter(self.mri_data, sigma=0.5)
        
        print("MRI数据预处理完成")
    
    def segment_tissues(self):
        """改进的组织分割算法"""
        print("正在进行组织分割...")

        # 创建分割掩码
        self.segmentation = np.zeros_like(self.mri_data, dtype=np.uint8)

        # 步骤1: 脑提取 - 获取整个头部区域
        print("  步骤1: 头部区域提取...")
        head_mask = self._extract_head_region()

        # 步骤2: 强度标准化和增强
        print("  步骤2: 强度标准化...")
        normalized_data = self._normalize_intensity(head_mask)

        # 步骤3: 多阈值分割
        print("  步骤3: 多阈值组织分割...")
        tissue_masks = self._multi_threshold_segmentation(normalized_data, head_mask)

        # 步骤4: 形态学后处理
        print("  步骤4: 形态学后处理...")
        tissue_masks = self._morphological_postprocessing(tissue_masks)

        # 步骤5: 分配最终标签
        print("  步骤5: 分配组织标签...")
        self._assign_tissue_labels(tissue_masks)

        # 打印统计信息
        self._print_segmentation_stats()

        print("组织分割完成")
    
    def _get_outer_surface_mask(self, mask):
        """获取外表面掩码"""
        # 形态学操作获取外表面
        eroded = morphology.binary_erosion(mask, morphology.ball(2))
        return mask & ~eroded
    
    def _get_brain_mask(self, high_intensity_mask):
        """获取脑掩码"""
        # 移除小的连通区域
        labeled = measure.label(high_intensity_mask)
        props = measure.regionprops(labeled)
        
        if props:
            # 选择最大的连通区域作为脑区域
            largest_region = max(props, key=lambda x: x.area)
            brain_mask = labeled == largest_region.label
            
            # 填充孔洞
            brain_mask = ndimage.binary_fill_holes(brain_mask)
            return brain_mask
        
        return high_intensity_mask
    
    def _segment_gray_white_matter(self, brain_mask):
        """分割灰质和白质"""
        brain_data = self.mri_data * brain_mask
        
        # 使用Otsu阈值分割灰质和白质
        from skimage.filters import threshold_otsu
        
        brain_values = brain_data[brain_mask]
        if len(brain_values) > 0:
            threshold = threshold_otsu(brain_values)
            
            white_matter_mask = (brain_data > threshold) & brain_mask
            gray_matter_mask = (brain_data <= threshold) & brain_mask & (brain_data > 0.1)
            
            return gray_matter_mask, white_matter_mask
        
        return np.zeros_like(brain_mask), np.zeros_like(brain_mask)

    def _extract_head_region(self):
        """提取头部区域"""
        # 使用Otsu阈值获取前景
        from skimage.filters import threshold_otsu

        # 计算全局阈值
        threshold = threshold_otsu(self.mri_data[self.mri_data > 0])

        # 创建初始头部掩码
        head_mask = self.mri_data > (threshold * 0.1)  # 使用较低的阈值确保包含所有头部组织

        # 填充孔洞
        head_mask = ndimage.binary_fill_holes(head_mask)

        # 移除小的连通区域，保留最大的连通区域
        labeled = measure.label(head_mask)
        props = measure.regionprops(labeled)

        if props:
            # 选择最大的连通区域
            largest_region = max(props, key=lambda x: x.area)
            head_mask = labeled == largest_region.label

        # 形态学闭运算，连接断开的区域
        head_mask = morphology.binary_closing(head_mask, morphology.ball(3))

        return head_mask

    def _normalize_intensity(self, head_mask):
        """强度标准化"""
        # 只在头部区域内进行标准化
        head_data = self.mri_data * head_mask

        # 计算头部区域的统计信息
        head_values = head_data[head_mask]

        if len(head_values) == 0:
            return self.mri_data

        # 使用百分位数进行鲁棒标准化
        p1, p99 = np.percentile(head_values, [1, 99])

        # 标准化到[0, 1]范围
        normalized = np.clip((self.mri_data - p1) / (p99 - p1), 0, 1)

        # 只保留头部区域
        normalized = normalized * head_mask

        return normalized

    def _multi_threshold_segmentation(self, normalized_data, head_mask):
        """多阈值分割"""
        tissue_masks = {}

        # 定义强度范围（基于T1加权图像的典型强度值）
        # 这些阈值是经验值，可能需要根据具体数据调整

        # 背景和空气
        background_mask = normalized_data < 0.05

        # 脑脊液 (CSF) - 低强度
        csf_mask = (normalized_data >= 0.05) & (normalized_data < 0.25) & head_mask

        # 灰质 - 中等强度
        gray_matter_mask = (normalized_data >= 0.25) & (normalized_data < 0.55) & head_mask

        # 白质 - 高强度
        white_matter_mask = (normalized_data >= 0.55) & (normalized_data < 0.85) & head_mask

        # 颅骨 - 非常高强度，但需要位置约束
        skull_mask = (normalized_data >= 0.15) & (normalized_data < 0.75) & head_mask

        # 头皮 - 最外层的中高强度区域
        scalp_mask = (normalized_data >= 0.2) & (normalized_data < 0.8) & head_mask

        # 使用距离变换来改进分割
        tissue_masks = self._refine_with_distance_transform(
            normalized_data, head_mask, background_mask, csf_mask,
            gray_matter_mask, white_matter_mask, skull_mask, scalp_mask
        )

        return tissue_masks

    def _refine_with_distance_transform(self, normalized_data, head_mask,
                                       background_mask, csf_mask, gray_matter_mask,
                                       white_matter_mask, skull_mask, scalp_mask):
        """使用距离变换改进分割"""

        # 计算到头部边界的距离
        head_distance = ndimage.distance_transform_edt(head_mask)

        # 获取头部的大致中心区域（脑区域）
        max_distance = np.max(head_distance)
        brain_core_mask = head_distance > (max_distance * 0.3)  # 内部30%区域作为脑核心

        # 改进脑组织分割
        # 白质通常在脑的中心
        refined_white_matter = white_matter_mask & brain_core_mask
        refined_white_matter = self._clean_mask(refined_white_matter, min_size=1000)

        # 灰质围绕白质
        brain_outer = brain_core_mask & (head_distance > (max_distance * 0.2))
        refined_gray_matter = gray_matter_mask & brain_outer & ~refined_white_matter
        refined_gray_matter = self._clean_mask(refined_gray_matter, min_size=500)

        # 脑脊液在脑室和脑表面
        brain_region = refined_white_matter | refined_gray_matter
        brain_expanded = morphology.binary_dilation(brain_region, morphology.ball(2))
        refined_csf = csf_mask & brain_expanded & ~brain_region
        refined_csf = self._clean_mask(refined_csf, min_size=100)

        # 颅骨在脑组织外围
        brain_with_csf = brain_region | refined_csf
        brain_expanded_large = morphology.binary_dilation(brain_with_csf, morphology.ball(5))
        skull_region = head_mask & ~brain_expanded_large & (head_distance > (max_distance * 0.1))
        refined_skull = skull_mask & skull_region
        refined_skull = self._clean_mask(refined_skull, min_size=200)

        # 头皮是最外层
        inner_tissues = brain_with_csf | refined_skull
        inner_expanded = morphology.binary_dilation(inner_tissues, morphology.ball(3))
        scalp_region = head_mask & ~inner_expanded
        refined_scalp = scalp_mask & scalp_region
        refined_scalp = self._clean_mask(refined_scalp, min_size=500)

        return {
            'background': background_mask,
            'scalp': refined_scalp,
            'skull': refined_skull,
            'csf': refined_csf,
            'gray_matter': refined_gray_matter,
            'white_matter': refined_white_matter
        }

    def _clean_mask(self, mask, min_size=100):
        """清理掩码，移除小的连通区域"""
        if np.sum(mask) == 0:
            return mask

        # 标记连通区域
        labeled = measure.label(mask)

        # 移除小的区域
        cleaned = morphology.remove_small_objects(labeled, min_size=min_size)

        return cleaned > 0

    def _morphological_postprocessing(self, tissue_masks):
        """形态学后处理"""
        processed_masks = {}

        # 对每个组织进行形态学操作
        for tissue_name, mask in tissue_masks.items():
            if tissue_name == 'background':
                processed_masks[tissue_name] = mask
                continue

            # 闭运算：填充小孔
            processed = morphology.binary_closing(mask, morphology.ball(2))

            # 开运算：移除小的突起
            processed = morphology.binary_opening(processed, morphology.ball(1))

            # 再次清理小区域
            if tissue_name in ['scalp', 'skull']:
                processed = self._clean_mask(processed, min_size=1000)
            elif tissue_name in ['gray_matter', 'white_matter']:
                processed = self._clean_mask(processed, min_size=500)
            else:  # csf
                processed = self._clean_mask(processed, min_size=100)

            processed_masks[tissue_name] = processed

        # 解决重叠问题 - 按优先级分配
        processed_masks = self._resolve_overlaps(processed_masks)

        return processed_masks

    def _resolve_overlaps(self, tissue_masks):
        """解决组织掩码重叠问题"""
        # 优先级：白质 > 灰质 > 脑脊液 > 颅骨 > 头皮
        priority_order = ['white_matter', 'gray_matter', 'csf', 'skull', 'scalp']

        # 创建已分配的体素掩码
        assigned = np.zeros_like(list(tissue_masks.values())[0], dtype=bool)

        resolved_masks = {'background': tissue_masks['background']}

        for tissue in priority_order:
            if tissue in tissue_masks:
                # 移除已分配的体素
                clean_mask = tissue_masks[tissue] & ~assigned
                resolved_masks[tissue] = clean_mask
                assigned |= clean_mask

        return resolved_masks

    def _assign_tissue_labels(self, tissue_masks):
        """分配最终的组织标签"""
        # 标签映射
        label_map = {
            'background': 0,
            'scalp': 1,
            'skull': 2,
            'csf': 3,
            'gray_matter': 4,
            'white_matter': 5
        }

        # 初始化为背景
        self.segmentation = np.zeros_like(self.mri_data, dtype=np.uint8)

        # 分配标签
        for tissue_name, label in label_map.items():
            if tissue_name in tissue_masks:
                self.segmentation[tissue_masks[tissue_name]] = label

    def _print_segmentation_stats(self):
        """打印分割统计信息"""
        tissue_names = {
            0: '背景',
            1: '头皮',
            2: '颅骨',
            3: '脑脊液',
            4: '灰质',
            5: '白质'
        }

        total_voxels = self.segmentation.size

        print("分割统计:")
        for label, name in tissue_names.items():
            count = np.sum(self.segmentation == label)
            percentage = (count / total_voxels) * 100
            print(f"  {name}: {count:,} 体素 ({percentage:.1f}%)")

        # 检查是否有足够的脑组织
        brain_voxels = np.sum((self.segmentation == 4) | (self.segmentation == 5))
        brain_percentage = (brain_voxels / total_voxels) * 100

        if brain_percentage < 5:
            print(f"警告: 脑组织比例较低 ({brain_percentage:.1f}%)，可能需要调整分割参数")
        else:
            print(f"脑组织总计: {brain_voxels:,} 体素 ({brain_percentage:.1f}%)")

    def extract_surfaces(self):
        """从分割结果提取5层表面"""
        print("正在提取表面...")

        tissue_labels = {
            'scalp': 1,
            'skull': 2,
            'csf': 3,
            'gray_matter': 4,
            'white_matter': 5
        }

        for tissue_name, label in tissue_labels.items():
            print(f"提取{tissue_name}表面...")

            # 创建二值掩码
            mask = self.segmentation == label

            if np.sum(mask) == 0:
                print(f"警告: {tissue_name}区域为空")
                continue

            # 使用marching cubes算法提取表面
            try:
                verts, faces, normals, values = measure.marching_cubes(
                    mask.astype(float),
                    level=0.5,
                    spacing=self.header.get_zooms()
                )

                # 转换到世界坐标系
                verts_world = self._voxel_to_world(verts)

                # 简化网格
                verts_simplified, faces_simplified = self._simplify_mesh(verts_world, faces)

                self.surfaces[tissue_name] = {
                    'vertices': verts_simplified,
                    'faces': faces_simplified,
                    'normals': normals
                }

                print(f"{tissue_name}表面: {len(verts_simplified)}个顶点, {len(faces_simplified)}个面")

            except Exception as e:
                print(f"提取{tissue_name}表面失败: {e}")

        print("表面提取完成")

    def _voxel_to_world(self, verts):
        """将体素坐标转换为世界坐标"""
        # 添加齐次坐标
        verts_homo = np.column_stack([verts, np.ones(len(verts))])
        # 应用仿射变换
        verts_world = np.dot(verts_homo, self.affine.T)[:, :3]
        return verts_world

    def _simplify_mesh(self, vertices, faces, target_reduction=0.5):
        """简化网格以减少计算复杂度"""
        try:
            import trimesh

            # 创建trimesh对象
            mesh = trimesh.Trimesh(vertices=vertices, faces=faces)

            # 简化网格
            target_faces = int(len(faces) * target_reduction)
            simplified = mesh.simplify_quadric_decimation(target_faces)

            return simplified.vertices, simplified.faces

        except ImportError:
            print("警告: trimesh未安装，跳过网格简化")
            return vertices, faces
        except Exception as e:
            print(f"网格简化失败: {e}")
            return vertices, faces

    def build_bem_model(self):
        """构建BEM模型"""
        print("正在构建BEM模型...")

        if not self.surfaces:
            print("错误: 没有可用的表面数据")
            return False

        try:
            # 准备BEM表面数据
            bem_surfaces = []

            # 按照从外到内的顺序排列表面
            surface_order = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
            conductivity_values = []

            for surface_name in surface_order:
                if surface_name in self.surfaces:
                    surface_data = self.surfaces[surface_name]

                    # 创建MNE表面格式
                    bem_surface = {
                        'rr': surface_data['vertices'] / 1000.0,  # 转换为米
                        'tris': surface_data['faces'],
                        'ntri': len(surface_data['faces']),
                        'np': len(surface_data['vertices']),
                        'coord_frame': 0  # MRI坐标系
                    }

                    bem_surfaces.append(bem_surface)
                    conductivity_values.append(self.conductivities[surface_name])

                    print(f"添加{surface_name}表面到BEM模型")

            if len(bem_surfaces) == 0:
                print("错误: 没有有效的表面用于BEM模型")
                return False

            # 创建BEM模型
            self.bem_model = bem_surfaces

            # 计算BEM解
            print("正在计算BEM解...")
            self.bem_solution = self._compute_bem_solution(conductivity_values)

            print("BEM模型构建完成")
            return True

        except Exception as e:
            print(f"构建BEM模型失败: {e}")
            return False

    def _compute_bem_solution(self, conductivities):
        """计算BEM解"""
        try:
            # 这里应该实现实际的BEM求解
            # 由于复杂性，这里提供一个简化版本

            print(f"使用电导率值: {conductivities}")

            # 创建简化的BEM解结构
            bem_solution = {
                'surfaces': self.bem_model,
                'conductivities': conductivities,
                'method': 'simplified_bem',
                'is_sphere': False
            }

            return bem_solution

        except Exception as e:
            print(f"计算BEM解失败: {e}")
            return None

    def visualize_model(self, save_path=None):
        """可视化BEM模型"""
        try:
            import pyvista as pv

            print("正在创建3D可视化...")

            # 创建绘图器
            plotter = pv.Plotter()

            # 颜色映射
            colors = {
                'scalp': 'pink',
                'skull': 'white',
                'csf': 'lightblue',
                'gray_matter': 'gray',
                'white_matter': 'lightgray'
            }

            # 透明度设置
            alphas = {
                'scalp': 0.3,
                'skull': 0.4,
                'csf': 0.5,
                'gray_matter': 0.6,
                'white_matter': 0.8
            }

            # 添加每个表面
            for surface_name, surface_data in self.surfaces.items():
                vertices = surface_data['vertices']
                faces = surface_data['faces']

                # 创建PyVista网格
                mesh = pv.PolyData(vertices, np.column_stack([
                    np.full(len(faces), 3), faces
                ]).flatten())

                # 添加到绘图器
                plotter.add_mesh(
                    mesh,
                    color=colors.get(surface_name, 'red'),
                    opacity=alphas.get(surface_name, 0.5),
                    label=surface_name
                )

            # 设置相机和显示
            plotter.add_legend()
            plotter.show_axes()
            plotter.set_background('black')

            if save_path:
                plotter.screenshot(save_path)
                print(f"可视化结果已保存到: {save_path}")

            plotter.show()

        except ImportError:
            print("警告: pyvista未安装，无法进行3D可视化")
            self._plot_2d_slices()
        except Exception as e:
            print(f"可视化失败: {e}")

    def _plot_2d_slices(self):
        """绘制2D切片图"""
        if self.segmentation is None:
            print("没有分割数据可显示")
            return

        fig, axes = plt.subplots(2, 3, figsize=(15, 10))

        # 获取中心切片
        center_x = self.segmentation.shape[0] // 2
        center_y = self.segmentation.shape[1] // 2
        center_z = self.segmentation.shape[2] // 2

        # 矢状面
        axes[0, 0].imshow(self.mri_data[center_x, :, :], cmap='gray')
        axes[0, 0].set_title('原始MRI - 矢状面')
        axes[1, 0].imshow(self.segmentation[center_x, :, :], cmap='tab10')
        axes[1, 0].set_title('分割结果 - 矢状面')

        # 冠状面
        axes[0, 1].imshow(self.mri_data[:, center_y, :], cmap='gray')
        axes[0, 1].set_title('原始MRI - 冠状面')
        axes[1, 1].imshow(self.segmentation[:, center_y, :], cmap='tab10')
        axes[1, 1].set_title('分割结果 - 冠状面')

        # 轴状面
        axes[0, 2].imshow(self.mri_data[:, :, center_z], cmap='gray')
        axes[0, 2].set_title('原始MRI - 轴状面')
        axes[1, 2].imshow(self.segmentation[:, :, center_z], cmap='tab10')
        axes[1, 2].set_title('分割结果 - 轴状面')

        plt.tight_layout()
        plt.show()

    def save_model(self, output_dir):
        """保存BEM模型"""
        os.makedirs(output_dir, exist_ok=True)

        try:
            # 保存表面数据
            for surface_name, surface_data in self.surfaces.items():
                surface_file = os.path.join(output_dir, f"{surface_name}_surface.npz")
                np.savez(surface_file,
                        vertices=surface_data['vertices'],
                        faces=surface_data['faces'])
                print(f"保存{surface_name}表面到: {surface_file}")

            # 保存BEM解
            if self.bem_solution:
                bem_file = os.path.join(output_dir, "bem_solution.npz")
                np.savez(bem_file,
                        conductivities=self.bem_solution['conductivities'],
                        method=self.bem_solution['method'])
                print(f"保存BEM解到: {bem_file}")

            # 保存分割结果
            if self.segmentation is not None:
                seg_img = nib.Nifti1Image(self.segmentation, self.affine, self.header)
                seg_file = os.path.join(output_dir, "segmentation.nii.gz")
                nib.save(seg_img, seg_file)
                print(f"保存分割结果到: {seg_file}")

            print(f"模型保存完成，输出目录: {output_dir}")

        except Exception as e:
            print(f"保存模型失败: {e}")

    def get_model_info(self):
        """获取模型信息"""
        info = {
            'mri_shape': self.mri_data.shape if self.mri_data is not None else None,
            'voxel_size': self.header.get_zooms() if self.header else None,
            'surfaces': {},
            'conductivities': self.conductivities,
            'bem_model_ready': self.bem_model is not None,
            'bem_solution_ready': self.bem_solution is not None
        }

        for surface_name, surface_data in self.surfaces.items():
            info['surfaces'][surface_name] = {
                'vertices_count': len(surface_data['vertices']),
                'faces_count': len(surface_data['faces'])
            }

        return info
