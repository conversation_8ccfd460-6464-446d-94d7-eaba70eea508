#!/usr/bin/env python3
"""
可视化BEM模型结果
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
from mpl_toolkits.mplot3d import Axes3D

def load_surface(surface_file):
    """加载表面数据"""
    data = np.load(surface_file)
    return data['vertices'], data['faces']

def visualize_surfaces_2d():
    """2D可视化表面点云"""
    surfaces = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
    colors = ['pink', 'white', 'lightblue', 'gray', 'lightgray']
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, (surface, color) in enumerate(zip(surfaces, colors)):
        try:
            vertices, faces = load_surface(f'example_output/{surface}_surface.npz')
            
            # 投影到XY平面
            x, y = vertices[:, 0], vertices[:, 1]
            
            # 采样显示（避免点太多）
            sample_idx = np.random.choice(len(vertices), min(5000, len(vertices)), replace=False)
            
            axes[i].scatter(x[sample_idx], y[sample_idx], c=color, s=0.1, alpha=0.6)
            axes[i].set_title(f'{surface.replace("_", " ").title()} Surface\n({len(vertices)} vertices)')
            axes[i].set_xlabel('X (mm)')
            axes[i].set_ylabel('Y (mm)')
            axes[i].grid(True, alpha=0.3)
            
        except Exception as e:
            axes[i].text(0.5, 0.5, f'Error loading {surface}:\n{str(e)}', 
                        transform=axes[i].transAxes, ha='center', va='center')
    
    plt.tight_layout()
    plt.savefig('bem_surfaces_2d.png', dpi=150, bbox_inches='tight')
    plt.show()

def visualize_segmentation():
    """可视化分割结果"""
    try:
        # 加载分割结果
        seg_img = nib.load('example_output/segmentation.nii.gz')
        seg_data = seg_img.get_fdata()
        
        # 获取中心切片
        center_x = seg_data.shape[0] // 2
        center_y = seg_data.shape[1] // 2
        center_z = seg_data.shape[2] // 2
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 矢状面
        axes[0].imshow(seg_data[center_x, :, :], cmap='tab10', vmin=0, vmax=5)
        axes[0].set_title('Sagittal View')
        axes[0].set_xlabel('Z')
        axes[0].set_ylabel('Y')
        
        # 冠状面
        axes[1].imshow(seg_data[:, center_y, :], cmap='tab10', vmin=0, vmax=5)
        axes[1].set_title('Coronal View')
        axes[1].set_xlabel('Z')
        axes[1].set_ylabel('X')
        
        # 轴状面
        im = axes[2].imshow(seg_data[:, :, center_z], cmap='tab10', vmin=0, vmax=5)
        axes[2].set_title('Axial View')
        axes[2].set_xlabel('Y')
        axes[2].set_ylabel('X')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=axes, shrink=0.6)
        cbar.set_label('Tissue Type')
        cbar.set_ticks([0, 1, 2, 3, 4, 5])
        cbar.set_ticklabels(['Background', 'Scalp', 'Skull', 'CSF', 'Gray Matter', 'White Matter'])
        
        plt.tight_layout()
        plt.savefig('bem_segmentation.png', dpi=150, bbox_inches='tight')
        plt.show()
        
    except Exception as e:
        print(f"Error visualizing segmentation: {e}")

def print_model_summary():
    """打印模型摘要"""
    print("="*60)
    print("5层BEM模型构建结果摘要")
    print("="*60)
    
    surfaces = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
    conductivities = {'scalp': 0.33, 'skull': 0.0042, 'csf': 1.79, 'gray_matter': 0.33, 'white_matter': 0.14}
    
    total_vertices = 0
    total_faces = 0
    
    for surface in surfaces:
        try:
            vertices, faces = load_surface(f'example_output/{surface}_surface.npz')
            total_vertices += len(vertices)
            total_faces += len(faces)
            
            print(f"{surface.replace('_', ' ').title():15}: {len(vertices):8,} 顶点, {len(faces):8,} 面")
            print(f"{'':15}  电导率: {conductivities[surface]:8.4f} S/m")
            print()
            
        except Exception as e:
            print(f"{surface:15}: 加载失败 - {e}")
    
    print("-"*60)
    print(f"{'总计':15}: {total_vertices:8,} 顶点, {total_faces:8,} 面")
    print("="*60)
    
    # 加载BEM解信息
    try:
        bem_data = np.load('example_output/bem_solution.npz', allow_pickle=True)
        print("BEM解信息:")
        print(f"  方法: {bem_data['method']}")
        print(f"  电导率: {bem_data['conductivities']}")
        print("="*60)
    except Exception as e:
        print(f"BEM解加载失败: {e}")

if __name__ == "__main__":
    print("BEM模型结果可视化")
    print("="*40)
    
    # 打印摘要
    print_model_summary()
    
    # 2D可视化
    print("生成2D表面可视化...")
    visualize_surfaces_2d()
    
    # 分割可视化
    print("生成分割结果可视化...")
    visualize_segmentation()
    
    print("可视化完成！")
    print("生成的文件:")
    print("- bem_surfaces_2d.png: 表面点云2D投影")
    print("- bem_segmentation.png: 组织分割结果")
