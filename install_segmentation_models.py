#!/usr/bin/env python3
"""
安装开源脑分割模型的脚本
支持SynthSeg、FastSurfer、HD-BET等模型的自动安装
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import tarfile
import shutil
from pathlib import Path

class SegmentationModelInstaller:
    """分割模型安装器"""
    
    def __init__(self, install_dir="./models"):
        self.install_dir = Path(install_dir)
        self.install_dir.mkdir(exist_ok=True)
        
    def install_synthseg(self):
        """安装SynthSeg"""
        print("正在安装SynthSeg...")
        
        try:
            # 安装依赖
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'tensorflow>=2.5.0'], check=True)
            
            # 克隆SynthSeg仓库
            synthseg_dir = self.install_dir / "SynthSeg"
            if not synthseg_dir.exists():
                subprocess.run([
                    'git', 'clone', 
                    'https://github.com/BBillot/SynthSeg.git',
                    str(synthseg_dir)
                ], check=True)
            
            # 安装SynthSeg
            os.chdir(synthseg_dir)
            subprocess.run([sys.executable, 'setup.py', 'install'], check=True)
            
            print("✅ SynthSeg 安装成功")
            return True
            
        except Exception as e:
            print(f"❌ SynthSeg 安装失败: {e}")
            return False
    
    def install_fastsurfer(self):
        """安装FastSurfer"""
        print("正在安装FastSurfer...")
        
        try:
            # 安装依赖
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision'], check=True)
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'nibabel', 'numpy', 'scipy'], check=True)
            
            # 克隆FastSurfer仓库
            fastsurfer_dir = self.install_dir / "FastSurfer"
            if not fastsurfer_dir.exists():
                subprocess.run([
                    'git', 'clone', 
                    'https://github.com/Deep-MI/FastSurfer.git',
                    str(fastsurfer_dir)
                ], check=True)
            
            # 下载预训练模型
            model_dir = fastsurfer_dir / "checkpoints"
            model_dir.mkdir(exist_ok=True)
            
            model_url = "https://github.com/Deep-MI/FastSurfer/releases/download/v2.0.0/aparc_vinn_axial_v2.0.0.pkl"
            model_path = model_dir / "aparc_vinn_axial_v2.0.0.pkl"
            
            if not model_path.exists():
                print("下载FastSurfer预训练模型...")
                urllib.request.urlretrieve(model_url, model_path)
            
            print("✅ FastSurfer 安装成功")
            return True
            
        except Exception as e:
            print(f"❌ FastSurfer 安装失败: {e}")
            return False
    
    def install_hdbet(self):
        """安装HD-BET"""
        print("正在安装HD-BET...")
        
        try:
            # 安装HD-BET
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'HD-BET'], check=True)
            
            # 下载预训练模型
            print("下载HD-BET预训练模型...")
            subprocess.run(['hd-bet-download-models'], check=True)
            
            print("✅ HD-BET 安装成功")
            return True
            
        except Exception as e:
            print(f"❌ HD-BET 安装失败: {e}")
            return False
    
    def install_all(self):
        """安装所有模型"""
        print("开始安装所有分割模型...")
        
        results = {
            'synthseg': self.install_synthseg(),
            'fastsurfer': self.install_fastsurfer(),
            'hdbet': self.install_hdbet()
        }
        
        print("\n安装结果:")
        for model, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {model}: {status}")
        
        successful_installs = sum(results.values())
        print(f"\n总计: {successful_installs}/3 个模型安装成功")
        
        return results
    
    def create_environment_setup(self):
        """创建环境设置脚本"""
        setup_script = """#!/bin/bash
# 环境设置脚本

# 添加模型路径到Python路径
export PYTHONPATH="${PYTHONPATH}:$(pwd)/models/SynthSeg"
export PYTHONPATH="${PYTHONPATH}:$(pwd)/models/FastSurfer"

# FastSurfer环境变量
export FASTSURFER_HOME="$(pwd)/models/FastSurfer"
export SUBJECTS_DIR="$(pwd)/subjects"

# 创建subjects目录
mkdir -p $SUBJECTS_DIR

echo "环境设置完成"
echo "SynthSeg路径: $(pwd)/models/SynthSeg"
echo "FastSurfer路径: $(pwd)/models/FastSurfer"
echo "HD-BET: 已安装到系统"
"""
        
        setup_file = Path("setup_environment.sh")
        with open(setup_file, 'w') as f:
            f.write(setup_script)
        
        # 使脚本可执行
        os.chmod(setup_file, 0o755)
        
        print(f"✅ 环境设置脚本已创建: {setup_file}")

def main():
    """主函数"""
    print("="*60)
    print("开源脑分割模型安装器")
    print("="*60)
    
    installer = SegmentationModelInstaller()
    
    if len(sys.argv) > 1:
        model = sys.argv[1].lower()
        if model == 'synthseg':
            installer.install_synthseg()
        elif model == 'fastsurfer':
            installer.install_fastsurfer()
        elif model == 'hdbet':
            installer.install_hdbet()
        elif model == 'all':
            installer.install_all()
        else:
            print(f"未知模型: {model}")
            print("支持的模型: synthseg, fastsurfer, hdbet, all")
    else:
        # 交互式安装
        print("请选择要安装的模型:")
        print("1. SynthSeg (对比度无关的脑分割)")
        print("2. FastSurfer (快速FreeSurfer)")
        print("3. HD-BET (脑提取)")
        print("4. 安装所有模型")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            installer.install_synthseg()
        elif choice == '2':
            installer.install_fastsurfer()
        elif choice == '3':
            installer.install_hdbet()
        elif choice == '4':
            installer.install_all()
        elif choice == '0':
            print("退出安装")
            return
        else:
            print("无效选择")
            return
    
    # 创建环境设置脚本
    installer.create_environment_setup()
    
    print("\n" + "="*60)
    print("安装完成!")
    print("使用方法:")
    print("1. 运行 'source setup_environment.sh' 设置环境")
    print("2. 使用 AdvancedBEMBuilder 进行高精度分割")
    print("="*60)

if __name__ == "__main__":
    main()
