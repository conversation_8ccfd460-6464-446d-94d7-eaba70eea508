#!/usr/bin/env python3
"""
比较不同分割方法的结果
"""

import os
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt

def load_surface_stats(output_dir):
    """加载表面统计信息"""
    surfaces = ['scalp', 'skull', 'csf', 'gray_matter', 'white_matter']
    stats = {}
    
    for surface in surfaces:
        surface_file = os.path.join(output_dir, f'{surface}_surface.npz')
        if os.path.exists(surface_file):
            data = np.load(surface_file)
            stats[surface] = {
                'vertices': len(data['vertices']),
                'faces': len(data['faces'])
            }
        else:
            stats[surface] = {'vertices': 0, 'faces': 0}
    
    return stats

def load_segmentation_stats(output_dir):
    """加载分割统计信息"""
    seg_file = os.path.join(output_dir, 'segmentation.nii.gz')
    if not os.path.exists(seg_file):
        return None
    
    seg_img = nib.load(seg_file)
    seg_data = seg_img.get_fdata()
    
    tissue_names = {
        0: '背景',
        1: '头皮',
        2: '颅骨', 
        3: '脑脊液',
        4: '灰质',
        5: '白质'
    }
    
    total_voxels = seg_data.size
    stats = {}
    
    for label, name in tissue_names.items():
        count = np.sum(seg_data == label)
        percentage = (count / total_voxels) * 100
        stats[name] = {
            'count': int(count),
            'percentage': percentage
        }
    
    # 计算脑组织比例
    brain_voxels = np.sum((seg_data == 4) | (seg_data == 5))
    brain_percentage = (brain_voxels / total_voxels) * 100
    stats['脑组织总计'] = {
        'count': int(brain_voxels),
        'percentage': brain_percentage
    }
    
    return stats

def compare_methods():
    """比较不同方法的结果"""
    methods = {
        'traditional': 'traditional_bem_output',
        'example': 'example_output',
        'advanced': 'advanced_bem_output',
        'hdbet': 'hdbet_bem_output'
    }
    
    print("="*80)
    print("BEM模型构建结果比较")
    print("="*80)
    
    results = {}
    
    for method_name, output_dir in methods.items():
        if os.path.exists(output_dir):
            print(f"\n📊 {method_name.upper()} 方法结果:")
            print("-" * 50)
            
            # 表面统计
            surface_stats = load_surface_stats(output_dir)
            total_vertices = sum(s['vertices'] for s in surface_stats.values())
            total_faces = sum(s['faces'] for s in surface_stats.values())
            
            print("表面网格统计:")
            for surface, stats in surface_stats.items():
                if stats['vertices'] > 0:
                    print(f"  {surface:15}: {stats['vertices']:8,} 顶点, {stats['faces']:8,} 面")
            
            print(f"  {'总计':15}: {total_vertices:8,} 顶点, {total_faces:8,} 面")
            
            # 分割统计
            seg_stats = load_segmentation_stats(output_dir)
            if seg_stats:
                print("\n组织分割统计:")
                for tissue, stats in seg_stats.items():
                    if tissue != '脑组织总计':
                        print(f"  {tissue:8}: {stats['count']:10,} 体素 ({stats['percentage']:5.1f}%)")
                
                brain_stats = seg_stats['脑组织总计']
                print(f"  {'脑组织':8}: {brain_stats['count']:10,} 体素 ({brain_stats['percentage']:5.1f}%)")
            
            results[method_name] = {
                'surface_stats': surface_stats,
                'seg_stats': seg_stats,
                'total_vertices': total_vertices,
                'total_faces': total_faces
            }
        else:
            print(f"\n❌ {method_name.upper()}: 输出目录不存在 ({output_dir})")
    
    # 生成比较表格
    if len(results) > 1:
        print("\n" + "="*80)
        print("方法比较总结")
        print("="*80)
        
        print(f"{'方法':<12} {'总顶点':<12} {'总面':<12} {'脑组织比例':<12} {'状态'}")
        print("-" * 60)
        
        for method_name, result in results.items():
            vertices = result['total_vertices']
            faces = result['total_faces']
            
            if result['seg_stats'] and '脑组织总计' in result['seg_stats']:
                brain_pct = result['seg_stats']['脑组织总计']['percentage']
                status = "✅ 完成" if brain_pct > 3 else "⚠️  脑组织少"
            else:
                brain_pct = 0
                status = "❌ 无分割数据"
            
            print(f"{method_name:<12} {vertices:<12,} {faces:<12,} {brain_pct:<12.1f}% {status}")

def visualize_comparison():
    """可视化比较结果"""
    methods = ['traditional', 'example', 'advanced', 'hdbet']
    brain_percentages = []
    method_names = []
    
    for method in methods:
        output_dir = f"{method}_bem_output" if method != 'example' else 'example_output'
        if os.path.exists(output_dir):
            seg_stats = load_segmentation_stats(output_dir)
            if seg_stats and '脑组织总计' in seg_stats:
                brain_percentages.append(seg_stats['脑组织总计']['percentage'])
                method_names.append(method)
    
    if len(brain_percentages) > 1:
        plt.figure(figsize=(10, 6))
        bars = plt.bar(method_names, brain_percentages, 
                      color=['skyblue', 'lightgreen', 'orange', 'lightcoral'][:len(method_names)])
        
        plt.title('不同分割方法的脑组织比例比较', fontsize=14, fontweight='bold')
        plt.ylabel('脑组织比例 (%)', fontsize=12)
        plt.xlabel('分割方法', fontsize=12)
        
        # 添加数值标签
        for bar, pct in zip(bars, brain_percentages):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{pct:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        plt.grid(axis='y', alpha=0.3)
        plt.tight_layout()
        plt.savefig('method_comparison.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"\n📊 比较图表已保存为: method_comparison.png")

if __name__ == "__main__":
    compare_methods()
    print("\n" + "="*80)
    
    # 可视化比较
    try:
        visualize_comparison()
    except Exception as e:
        print(f"可视化失败: {e}")
    
    print("比较完成!")
