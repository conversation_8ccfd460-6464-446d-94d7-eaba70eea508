#!/usr/bin/env python3
"""
Clinical EEG Wavelet Decomposition Analysis
Following medical standards for epilepsy research

This script performs comprehensive wavelet analysis on EEG data from Guinea-Bissau and Nigeria
datasets, comparing epilepsy patients with control groups using clinically relevant frequency bands.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import pywt
from scipy import signal
from scipy.stats import ttest_ind
import gzip
import os
import warnings
warnings.filterwarnings('ignore')

# Set publication-quality plotting parameters
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

class ClinicalEEGAnalyzer:
    """
    Clinical EEG Wavelet Analysis following medical standards
    """
    
    def __init__(self):
        # Clinical EEG frequency bands (Hz)
        self.frequency_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8),
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, 100)
        }
        
        # Standard EEG electrode positions for Nigeria dataset
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Sampling frequency (estimated from data inspection)
        self.fs = 128  # Hz (common EEG sampling rate)
        
    def load_eeg_data(self, file_path, dataset_type='nigeria'):
        """
        Load and preprocess EEG data from compressed CSV files
        
        Parameters:
        -----------
        file_path : str
            Path to the compressed CSV file
        dataset_type : str
            'nigeria' or 'guinea_bissau'
            
        Returns:
        --------
        data : numpy.ndarray
            EEG data matrix (channels x time)
        """
        try:
            with gzip.open(file_path, 'rt') as f:
                if dataset_type == 'nigeria':
                    # Nigeria dataset has header with channel names
                    df = pd.read_csv(f)
                    # Extract EEG channels (exclude metadata columns) and ensure numeric
                    eeg_data = df[self.eeg_channels].apply(pd.to_numeric, errors='coerce').values.T
                else:
                    # Guinea-Bissau dataset format (no header, different structure)
                    df = pd.read_csv(f, header=None)
                    # Assume first 14 columns are EEG channels, convert to numeric
                    eeg_data = pd.to_numeric(df.iloc[:, 1:15].values.flatten(), errors='coerce').reshape(df.shape[0], 14).T
                    
            return eeg_data
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def preprocess_signal(self, signal_data, channel_idx=0):
        """
        Preprocess EEG signal for wavelet analysis
        
        Parameters:
        -----------
        signal_data : numpy.ndarray
            Raw EEG signal
        channel_idx : int
            Channel index to analyze
            
        Returns:
        --------
        processed_signal : numpy.ndarray
            Preprocessed EEG signal
        """
        if signal_data is None or signal_data.shape[0] <= channel_idx:
            return None
            
        # Select channel and ensure numeric type
        eeg_signal = signal_data[channel_idx, :].astype(float)

        # Remove NaN values
        eeg_signal = eeg_signal[~np.isnan(eeg_signal)]

        if len(eeg_signal) == 0:
            return None

        # Remove DC component
        eeg_signal = eeg_signal - np.mean(eeg_signal)
        
        # Apply bandpass filter (0.5-100 Hz) for clinical EEG
        nyquist = self.fs / 2
        low_freq = 0.5 / nyquist
        high_freq = min(100, nyquist - 1) / nyquist
        
        b, a = signal.butter(4, [low_freq, high_freq], btype='band')
        filtered_signal = signal.filtfilt(b, a, eeg_signal)
        
        return filtered_signal
    
    def wavelet_decomposition(self, eeg_signal, wavelet='db4', levels=8):
        """
        Perform multi-level wavelet decomposition
        
        Parameters:
        -----------
        eeg_signal : numpy.ndarray
            Preprocessed EEG signal
        wavelet : str
            Wavelet function ('db4', 'db8', 'coif4', etc.)
        levels : int
            Number of decomposition levels
            
        Returns:
        --------
        coeffs : list
            Wavelet coefficients [cA_n, cD_n, cD_n-1, ..., cD_1]
        """
        coeffs = pywt.wavedec(eeg_signal, wavelet, level=levels)
        return coeffs
    
    def extract_frequency_bands(self, coeffs, levels=8):
        """
        Extract clinical frequency bands from wavelet coefficients
        
        Parameters:
        -----------
        coeffs : list
            Wavelet coefficients
        levels : int
            Number of decomposition levels
            
        Returns:
        --------
        bands : dict
            Dictionary containing reconstructed signals for each frequency band
        """
        bands = {}
        
        # Calculate frequency ranges for each level
        # Level 1: fs/4 to fs/2, Level 2: fs/8 to fs/4, etc.
        
        # Gamma (30-100 Hz) - typically levels 1-2
        gamma_coeffs = [np.zeros_like(coeffs[0])] + [coeffs[1]] + [np.zeros_like(c) for c in coeffs[2:]]
        bands['Gamma'] = pywt.waverec(gamma_coeffs, 'db4')
        
        # Beta (13-30 Hz) - typically levels 2-3
        beta_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(coeffs[1])] + [coeffs[2]] + [np.zeros_like(c) for c in coeffs[3:]]
        bands['Beta'] = pywt.waverec(beta_coeffs, 'db4')
        
        # Alpha (8-13 Hz) - typically level 4
        alpha_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(c) for c in coeffs[1:4]] + [coeffs[4]] + [np.zeros_like(c) for c in coeffs[5:]]
        bands['Alpha'] = pywt.waverec(alpha_coeffs, 'db4')
        
        # Theta (4-8 Hz) - typically level 5
        theta_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(c) for c in coeffs[1:5]] + [coeffs[5]] + [np.zeros_like(c) for c in coeffs[6:]]
        bands['Theta'] = pywt.waverec(theta_coeffs, 'db4')
        
        # Delta (0.5-4 Hz) - typically levels 6-8 and approximation
        delta_coeffs = [coeffs[0]] + [np.zeros_like(c) for c in coeffs[1:6]] + coeffs[6:]
        bands['Delta'] = pywt.waverec(delta_coeffs, 'db4')
        
        return bands
    
    def calculate_band_power(self, signal_data, band_name):
        """
        Calculate relative power in frequency band

        Parameters:
        -----------
        signal_data : numpy.ndarray
            Signal data for specific frequency band
        band_name : str
            Name of frequency band

        Returns:
        --------
        power : float
            Relative power in the frequency band
        """
        return np.mean(signal_data ** 2)

    def plot_wavelet_analysis(self, eeg_signal, coeffs, bands, title="EEG Wavelet Analysis", save_path=None):
        """
        Create comprehensive wavelet analysis visualization

        Parameters:
        -----------
        eeg_signal : numpy.ndarray
            Original EEG signal
        coeffs : list
            Wavelet coefficients
        bands : dict
            Frequency band signals
        title : str
            Plot title
        save_path : str
            Path to save the figure
        """
        fig, axes = plt.subplots(6, 1, figsize=(15, 12))

        # Time axis
        time = np.arange(len(eeg_signal)) / self.fs

        # Original signal
        axes[0].plot(time, eeg_signal, 'k-', linewidth=0.8)
        axes[0].set_title(f'{title} - Original EEG Signal', fontweight='bold')
        axes[0].set_ylabel('Amplitude (μV)')
        axes[0].grid(True, alpha=0.3)

        # Frequency bands
        colors = ['red', 'orange', 'green', 'blue', 'purple']
        band_names = ['Delta', 'Theta', 'Alpha', 'Beta', 'Gamma']

        for i, (band_name, color) in enumerate(zip(band_names, colors)):
            if band_name in bands:
                band_signal = bands[band_name]
                # Ensure same length as original signal
                if len(band_signal) != len(eeg_signal):
                    band_signal = signal.resample(band_signal, len(eeg_signal))

                band_time = np.arange(len(band_signal)) / self.fs
                axes[i+1].plot(band_time, band_signal, color=color, linewidth=0.8)
                freq_range = self.frequency_bands[band_name]
                axes[i+1].set_title(f'{band_name} Band ({freq_range[0]}-{freq_range[1]} Hz)',
                                   fontweight='bold')
                axes[i+1].set_ylabel('Amplitude (μV)')
                axes[i+1].grid(True, alpha=0.3)

        axes[-1].set_xlabel('Time (seconds)')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def plot_time_frequency_analysis(self, eeg_signal, title="Time-Frequency Analysis", save_path=None):
        """
        Create time-frequency spectrogram using continuous wavelet transform

        Parameters:
        -----------
        eeg_signal : numpy.ndarray
            EEG signal
        title : str
            Plot title
        save_path : str
            Path to save the figure
        """
        # Use scipy spectrogram as alternative to CWT for more stable results
        frequencies, times, Sxx = signal.spectrogram(eeg_signal, fs=self.fs,
                                                     window='hann', nperseg=256,
                                                     noverlap=128, nfft=512)

        # Limit frequency range to clinical EEG bands
        freq_mask = frequencies <= 50
        frequencies = frequencies[freq_mask]
        Sxx = Sxx[freq_mask, :]

        # Create time-frequency plot
        fig, ax = plt.subplots(figsize=(12, 8))

        # Plot spectrogram
        im = ax.imshow(10 * np.log10(Sxx), aspect='auto', cmap='jet',
                      extent=[times[0], times[-1], frequencies[0], frequencies[-1]],
                      origin='lower')

        # Add frequency band boundaries
        band_colors = ['white', 'cyan', 'yellow', 'magenta', 'red']
        band_names = ['Delta', 'Theta', 'Alpha', 'Beta', 'Gamma']

        for i, (band_name, color) in enumerate(zip(band_names, band_colors)):
            freq_range = self.frequency_bands[band_name]
            ax.axhline(y=freq_range[0], color=color, linestyle='--', alpha=0.7, linewidth=2)
            ax.axhline(y=freq_range[1], color=color, linestyle='--', alpha=0.7, linewidth=2)

            # Add band labels
            ax.text(times[-1]*0.02, np.mean(freq_range), band_name,
                   color=color, fontweight='bold', fontsize=10)

        ax.set_xlabel('Time (seconds)')
        ax.set_ylabel('Frequency (Hz)')
        ax.set_title(f'{title} - Time-Frequency Spectrogram', fontweight='bold')

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Power (dB)', rotation=270, labelpad=20)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def compare_groups(self, epilepsy_data, control_data, title="Group Comparison"):
        """
        Compare frequency band power between epilepsy and control groups

        Parameters:
        -----------
        epilepsy_data : dict
            Dictionary containing band powers for epilepsy group
        control_data : dict
            Dictionary containing band powers for control group
        title : str
            Plot title
        """
        band_names = list(self.frequency_bands.keys())

        # Prepare data for plotting
        epilepsy_powers = []
        control_powers = []
        p_values = []

        for band in band_names:
            epi_power = epilepsy_data.get(band, [])
            ctrl_power = control_data.get(band, [])

            if len(epi_power) > 0 and len(ctrl_power) > 0:
                epilepsy_powers.append(epi_power)
                control_powers.append(ctrl_power)

                # Perform statistical test
                t_stat, p_val = ttest_ind(epi_power, ctrl_power)
                p_values.append(p_val)
            else:
                epilepsy_powers.append([0])
                control_powers.append([0])
                p_values.append(1.0)

        # Create comparison plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Box plot comparison
        positions = np.arange(len(band_names))

        # Epilepsy group
        bp1 = ax1.boxplot([ep for ep in epilepsy_powers], positions=positions-0.2,
                         widths=0.3, patch_artist=True,
                         boxprops=dict(facecolor='red', alpha=0.7),
                         medianprops=dict(color='darkred', linewidth=2))

        # Control group
        bp2 = ax1.boxplot([cp for cp in control_powers], positions=positions+0.2,
                         widths=0.3, patch_artist=True,
                         boxprops=dict(facecolor='blue', alpha=0.7),
                         medianprops=dict(color='darkblue', linewidth=2))

        ax1.set_xticks(positions)
        ax1.set_xticklabels(band_names)
        ax1.set_ylabel('Relative Power')
        ax1.set_title(f'{title} - Frequency Band Power Comparison', fontweight='bold')
        ax1.legend([bp1["boxes"][0], bp2["boxes"][0]], ['Epilepsy', 'Control'])
        ax1.grid(True, alpha=0.3)

        # Statistical significance plot
        colors = ['red' if p < 0.05 else 'gray' for p in p_values]
        bars = ax2.bar(band_names, [-np.log10(p) for p in p_values], color=colors, alpha=0.7)
        ax2.axhline(y=-np.log10(0.05), color='red', linestyle='--',
                   label='p = 0.05 threshold')
        ax2.set_ylabel('-log10(p-value)')
        ax2.set_title('Statistical Significance', fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Add p-values as text
        for i, (bar, p_val) in enumerate(zip(bars, p_values)):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'p={p_val:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.show()


def load_sample_data():
    """
    Load representative samples from both datasets

    Returns:
    --------
    samples : dict
        Dictionary containing loaded EEG samples
    """
    analyzer = ClinicalEEGAnalyzer()
    samples = {
        'nigeria_epilepsy': [],
        'nigeria_control': [],
        'guinea_bissau_epilepsy': [],
        'guinea_bissau_control': []
    }

    # Load Nigeria samples
    nigeria_path = "1252141/EEGs_Nigeria"
    if os.path.exists(nigeria_path):
        # Sample epilepsy files (500+ series)
        epilepsy_files = ['signal-500-1.csv.gz', 'signal-501-1.csv.gz', 'signal-502-1.csv.gz']
        for file in epilepsy_files:
            file_path = os.path.join(nigeria_path, file)
            if os.path.exists(file_path):
                data = analyzer.load_eeg_data(file_path, 'nigeria')
                if data is not None:
                    samples['nigeria_epilepsy'].append(data)

        # Sample control files (6-147 series)
        control_files = ['signal-6-1.csv.gz', 'signal-9-1.csv.gz', 'signal-10-1.csv.gz']
        for file in control_files:
            file_path = os.path.join(nigeria_path, file)
            if os.path.exists(file_path):
                data = analyzer.load_eeg_data(file_path, 'nigeria')
                if data is not None:
                    samples['nigeria_control'].append(data)

    # Load Guinea-Bissau samples
    gb_path = "1252141/EEGs_Guinea-Bissau"
    if os.path.exists(gb_path):
        # Load metadata to identify groups
        metadata_path = "1252141/metadata_guineabissau.csv"
        if os.path.exists(metadata_path):
            metadata = pd.read_csv(metadata_path)

            # Sample epilepsy subjects
            epilepsy_subjects = metadata[metadata['Group'] == 'Epilepsy']['subject.id'].head(3).values
            for subject_id in epilepsy_subjects:
                file_path = os.path.join(gb_path, f'signal-{subject_id}.csv.gz')
                if os.path.exists(file_path):
                    data = analyzer.load_eeg_data(file_path, 'guinea_bissau')
                    if data is not None:
                        samples['guinea_bissau_epilepsy'].append(data)

            # Sample control subjects
            control_subjects = metadata[metadata['Group'] == 'Control']['subject.id'].head(3).values
            for subject_id in control_subjects:
                file_path = os.path.join(gb_path, f'signal-{subject_id}.csv.gz')
                if os.path.exists(file_path):
                    data = analyzer.load_eeg_data(file_path, 'guinea_bissau')
                    if data is not None:
                        samples['guinea_bissau_control'].append(data)

    return samples


def main():
    """
    Main function to perform comprehensive EEG wavelet analysis
    """
    print("Clinical EEG Wavelet Decomposition Analysis")
    print("=" * 50)

    # Initialize analyzer
    analyzer = ClinicalEEGAnalyzer()

    # Load sample data
    print("Loading EEG data samples...")
    samples = load_sample_data()

    # Check if data was loaded successfully
    total_samples = sum(len(samples[key]) for key in samples.keys())
    if total_samples == 0:
        print("No EEG data found. Please check file paths.")
        return

    print(f"Loaded {total_samples} EEG samples:")
    for key, value in samples.items():
        print(f"  {key}: {len(value)} samples")

    # Analyze representative samples
    results = {
        'epilepsy_powers': {band: [] for band in analyzer.frequency_bands.keys()},
        'control_powers': {band: [] for band in analyzer.frequency_bands.keys()}
    }

    # Process Nigeria epilepsy samples
    print("\nAnalyzing Nigeria epilepsy samples...")
    for i, data in enumerate(samples['nigeria_epilepsy'][:2]):  # Analyze first 2 samples
        print(f"Processing epilepsy sample {i+1}...")

        # Preprocess signal (use first channel)
        eeg_signal = analyzer.preprocess_signal(data, channel_idx=0)
        if eeg_signal is None:
            continue

        # Perform wavelet decomposition
        coeffs = analyzer.wavelet_decomposition(eeg_signal)
        bands = analyzer.extract_frequency_bands(coeffs)

        # Calculate band powers
        for band_name, band_signal in bands.items():
            power = analyzer.calculate_band_power(band_signal, band_name)
            results['epilepsy_powers'][band_name].append(power)

        # Create visualization for first sample
        if i == 0:
            analyzer.plot_wavelet_analysis(
                eeg_signal, coeffs, bands,
                title="Nigeria Epilepsy Patient - Channel AF3",
                save_path="nigeria_epilepsy_wavelet.png"
            )

            analyzer.plot_time_frequency_analysis(
                eeg_signal,
                title="Nigeria Epilepsy Patient - Channel AF3",
                save_path="nigeria_epilepsy_spectrogram.png"
            )

    # Process Nigeria control samples
    print("\nAnalyzing Nigeria control samples...")
    for i, data in enumerate(samples['nigeria_control'][:2]):  # Analyze first 2 samples
        print(f"Processing control sample {i+1}...")

        # Preprocess signal (use first channel)
        eeg_signal = analyzer.preprocess_signal(data, channel_idx=0)
        if eeg_signal is None:
            continue

        # Perform wavelet decomposition
        coeffs = analyzer.wavelet_decomposition(eeg_signal)
        bands = analyzer.extract_frequency_bands(coeffs)

        # Calculate band powers
        for band_name, band_signal in bands.items():
            power = analyzer.calculate_band_power(band_signal, band_name)
            results['control_powers'][band_name].append(power)

        # Create visualization for first sample
        if i == 0:
            analyzer.plot_wavelet_analysis(
                eeg_signal, coeffs, bands,
                title="Nigeria Control Subject - Channel AF3",
                save_path="nigeria_control_wavelet.png"
            )

            analyzer.plot_time_frequency_analysis(
                eeg_signal,
                title="Nigeria Control Subject - Channel AF3",
                save_path="nigeria_control_spectrogram.png"
            )

    # Process Guinea-Bissau samples if available
    if len(samples['guinea_bissau_epilepsy']) > 0:
        print("\nAnalyzing Guinea-Bissau epilepsy samples...")
        for i, data in enumerate(samples['guinea_bissau_epilepsy'][:1]):
            print(f"Processing Guinea-Bissau epilepsy sample {i+1}...")

            # Preprocess signal (use first channel)
            eeg_signal = analyzer.preprocess_signal(data, channel_idx=0)
            if eeg_signal is None:
                continue

            # Perform wavelet decomposition
            coeffs = analyzer.wavelet_decomposition(eeg_signal)
            bands = analyzer.extract_frequency_bands(coeffs)

            # Calculate band powers
            for band_name, band_signal in bands.items():
                power = analyzer.calculate_band_power(band_signal, band_name)
                results['epilepsy_powers'][band_name].append(power)

            # Create visualization
            analyzer.plot_wavelet_analysis(
                eeg_signal, coeffs, bands,
                title="Guinea-Bissau Epilepsy Patient",
                save_path="guinea_bissau_epilepsy_wavelet.png"
            )

    if len(samples['guinea_bissau_control']) > 0:
        print("\nAnalyzing Guinea-Bissau control samples...")
        for i, data in enumerate(samples['guinea_bissau_control'][:1]):
            print(f"Processing Guinea-Bissau control sample {i+1}...")

            # Preprocess signal (use first channel)
            eeg_signal = analyzer.preprocess_signal(data, channel_idx=0)
            if eeg_signal is None:
                continue

            # Perform wavelet decomposition
            coeffs = analyzer.wavelet_decomposition(eeg_signal)
            bands = analyzer.extract_frequency_bands(coeffs)

            # Calculate band powers
            for band_name, band_signal in bands.items():
                power = analyzer.calculate_band_power(band_signal, band_name)
                results['control_powers'][band_name].append(power)

            # Create visualization
            analyzer.plot_wavelet_analysis(
                eeg_signal, coeffs, bands,
                title="Guinea-Bissau Control Subject",
                save_path="guinea_bissau_control_wavelet.png"
            )

    # Compare groups
    print("\nPerforming statistical comparison...")
    analyzer.compare_groups(
        results['epilepsy_powers'],
        results['control_powers'],
        title="Epilepsy vs Control Groups"
    )

    # Print summary statistics
    print("\nSummary Statistics:")
    print("-" * 30)
    for band in analyzer.frequency_bands.keys():
        epi_powers = results['epilepsy_powers'][band]
        ctrl_powers = results['control_powers'][band]

        if len(epi_powers) > 0 and len(ctrl_powers) > 0:
            epi_mean = np.mean(epi_powers)
            ctrl_mean = np.mean(ctrl_powers)

            print(f"{band} Band:")
            print(f"  Epilepsy: {epi_mean:.4f} ± {np.std(epi_powers):.4f}")
            print(f"  Control:  {ctrl_mean:.4f} ± {np.std(ctrl_powers):.4f}")
            print(f"  Ratio:    {epi_mean/ctrl_mean:.2f}")
            print()

    print("Analysis completed successfully!")
    print("Generated files:")
    print("- nigeria_epilepsy_wavelet.png")
    print("- nigeria_epilepsy_spectrogram.png")
    print("- nigeria_control_wavelet.png")
    print("- nigeria_control_spectrogram.png")
    if len(samples['guinea_bissau_epilepsy']) > 0:
        print("- guinea_bissau_epilepsy_wavelet.png")
    if len(samples['guinea_bissau_control']) > 0:
        print("- guinea_bissau_control_wavelet.png")


if __name__ == "__main__":
    main()
