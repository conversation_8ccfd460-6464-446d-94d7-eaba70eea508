#!/usr/bin/env python3
"""
快速安装HD-BET的脚本
HD-BET是最容易安装的深度学习脑提取工具
"""

import subprocess
import sys

def install_hdbet():
    """安装HD-BET"""
    print("正在安装HD-BET...")
    print("HD-BET是一个专业的脑提取深度学习工具")
    
    try:
        # 安装HD-BET
        print("步骤1: 安装HD-BET包...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'HD-BET'], check=True)
        
        # 下载预训练模型
        print("步骤2: 下载预训练模型...")
        try:
            subprocess.run(['hd-bet-download-models'], check=True)
        except subprocess.CalledProcessError:
            print("注意: 模型下载可能需要手动运行 'hd-bet-download-models'")
        
        print("✅ HD-BET 安装成功!")
        print("\n现在您可以使用:")
        print("  python advanced_main.py sub-1_T1w.nii --method hd-bet")
        
        return True
        
    except Exception as e:
        print(f"❌ HD-BET 安装失败: {e}")
        print("\n手动安装方法:")
        print("  pip install HD-BET")
        print("  hd-bet-download-models")
        return False

def test_hdbet():
    """测试HD-BET是否可用"""
    try:
        result = subprocess.run(['hd-bet', '--help'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ HD-BET 已安装并可用")
            return True
        else:
            print("❌ HD-BET 不可用")
            return False
    except FileNotFoundError:
        print("❌ HD-BET 未安装")
        return False

if __name__ == "__main__":
    print("HD-BET 快速安装器")
    print("="*40)
    
    # 检查是否已安装
    if test_hdbet():
        print("HD-BET 已经安装，无需重复安装")
    else:
        # 安装HD-BET
        success = install_hdbet()
        
        if success:
            print("\n测试安装...")
            test_hdbet()
        
    print("\n" + "="*40)
    print("完成!")
