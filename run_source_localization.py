#!/usr/bin/env python3
"""
EEG源定位主程序
使用HD-BET 5层BEM模型进行多种方法的EEG源定位
"""

import os
import sys
import argparse
import numpy as np
from eeg_source_localization import EEGSourceLocalizer

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='EEG源定位系统')
    parser.add_argument('--bem-dir', default='hdbet_bem_output',
                       help='BEM模型输出目录 (默认: hdbet_bem_output)')
    parser.add_argument('--eeg-file', default='signal-1.csv.gz',
                       help='EEG信号文件 (默认: signal-1.csv.gz)')
    parser.add_argument('--output-dir', default='source_localization_results',
                       help='输出目录 (默认: source_localization_results)')
    parser.add_argument('--methods', nargs='+', 
                       choices=['MNE', 'sLORETA', 'dSPM', 'MUSIC', 'LCMV', 'all'],
                       default=['all'],
                       help='要运行的源定位方法')
    parser.add_argument('--visualize', '-v', action='store_true',
                       help='生成可视化结果')
    parser.add_argument('--time-point', type=int,
                       help='可视化的时间点 (默认: 自动选择最大能量时间点)')
    
    args = parser.parse_args()
    
    print("="*70)
    print("EEG源定位系统")
    print("基于HD-BET 5层BEM模型的多方法源定位")
    print("="*70)
    
    # 检查输入文件
    if not os.path.exists(args.eeg_file):
        print(f"错误: EEG文件不存在: {args.eeg_file}")
        sys.exit(1)
    
    if not os.path.exists(args.bem_dir):
        print(f"错误: BEM模型目录不存在: {args.bem_dir}")
        print("请先运行BEM模型构建:")
        print("  python advanced_main.py sub-1_T1w.nii --method hd-bet")
        sys.exit(1)
    
    try:
        # 初始化源定位器
        localizer = EEGSourceLocalizer(
            bem_output_dir=args.bem_dir,
            eeg_file=args.eeg_file
        )
        
        # 运行源定位
        if 'all' in args.methods:
            # 运行所有方法
            success = localizer.run_all_methods()
        else:
            # 运行指定方法
            success = run_selected_methods(localizer, args.methods)
        
        if not success:
            print("源定位失败")
            sys.exit(1)
        
        # 分析结果
        print("\n" + "="*70)
        print("结果分析")
        print("="*70)
        
        sorted_methods = localizer.analyze_results()
        
        # 可视化
        if args.visualize:
            print("\n" + "="*70)
            print("生成可视化")
            print("="*70)
            
            localizer.visualize_results(
                time_point=args.time_point,
                save_dir=args.output_dir
            )
        
        # 保存结果
        save_results(localizer, args.output_dir)
        
        print("\n" + "="*70)
        print("EEG源定位完成!")
        print(f"结果保存在: {args.output_dir}")
        print("="*70)
        
        # 显示最佳方法
        if sorted_methods:
            best_method = sorted_methods[0][0]
            print(f"\n🏆 推荐方法: {best_method} (最大源强度: {sorted_methods[0][1]:.6f})")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def run_selected_methods(localizer, methods):
    """运行选定的方法"""
    print(f"运行选定的方法: {methods}")
    
    # 加载数据
    if not localizer.load_eeg_data():
        return False
    
    if not localizer.load_bem_model():
        return False
    
    if not localizer.create_source_space():
        return False
    
    if not localizer.compute_forward_model():
        return False
    
    # 方法映射
    method_map = {
        'MNE': localizer.minimum_norm_estimation,
        'sLORETA': localizer.sloreta,
        'dSPM': localizer.dspm,
        'MUSIC': localizer.music,
        'LCMV': localizer.beamformer_lcmv
    }
    
    successful_methods = []
    
    for method_name in methods:
        if method_name in method_map:
            print(f"\n{'='*40}")
            print(f"运行 {method_name}")
            print(f"{'='*40}")
            
            try:
                if method_map[method_name]():
                    successful_methods.append(method_name)
                    print(f"✅ {method_name} 成功完成")
                else:
                    print(f"❌ {method_name} 失败")
            except Exception as e:
                print(f"❌ {method_name} 异常: {e}")
        else:
            print(f"警告: 未知方法 {method_name}")
    
    print(f"\n成功完成的方法: {successful_methods}")
    return len(successful_methods) > 0

def save_results(localizer, output_dir):
    """保存源定位结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"保存结果到: {output_dir}")
    
    for method_name, result in localizer.source_estimates.items():
        # 保存源强度数据
        np.save(f"{output_dir}/{method_name}_source_strength.npy", result['data'])
        
        # 保存源位置
        np.save(f"{output_dir}/{method_name}_source_positions.npy", result['vertices'])
        
        # 保存元数据
        metadata = {
            'method': result['method'],
            'n_sources': result['data'].shape[0],
            'n_timepoints': result['data'].shape[1],
            'max_strength': np.max(result['data']),
            'mean_strength': np.mean(result['data'])
        }
        
        # 添加方法特定参数
        if 'lambda' in result:
            metadata['lambda'] = result['lambda']
        if 'n_sources' in result:
            metadata['n_sources_est'] = result['n_sources']
        if 'reg_param' in result:
            metadata['reg_param'] = result['reg_param']
        
        import json
        with open(f"{output_dir}/{method_name}_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"  {method_name}: 数据和元数据已保存")
    
    # 保存EEG数据信息
    eeg_info = {
        'n_channels': len(localizer.eeg_info['ch_names']),
        'channel_names': localizer.eeg_info['ch_names'],
        'sampling_rate': localizer.eeg_info['sfreq'],
        'n_timepoints': localizer.eeg_data.get_data().shape[1]
    }
    
    with open(f"{output_dir}/eeg_info.json", 'w') as f:
        json.dump(eeg_info, f, indent=2)
    
    print("  EEG信息已保存")

def demo_run():
    """演示运行"""
    print("EEG源定位演示")
    print("="*50)
    
    # 检查必要文件
    required_files = ['signal-1.csv.gz', 'hdbet_bem_output']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        
        if 'hdbet_bem_output' in missing_files:
            print("\n请先运行BEM模型构建:")
            print("  python advanced_main.py sub-1_T1w.nii --method hd-bet")
        
        return
    
    # 运行演示
    try:
        localizer = EEGSourceLocalizer()
        
        print("运行快速演示 (仅MNE和sLORETA)...")
        
        # 加载数据
        localizer.load_eeg_data()
        localizer.load_bem_model()
        localizer.create_source_space()
        localizer.compute_forward_model()
        
        # 运行两种方法
        localizer.minimum_norm_estimation()
        localizer.sloreta()
        
        # 分析和可视化
        localizer.analyze_results()
        localizer.visualize_results(save_dir='demo_results')
        
        print("\n演示完成! 结果保存在 demo_results/ 目录")
        
    except Exception as e:
        print(f"演示失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 没有参数时运行演示
        demo_run()
    else:
        # 有参数时运行主程序
        main()
